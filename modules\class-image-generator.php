<?php
/**
 * Image Generator Module for Rife PageGenerator
 * 
 * This module generates dummy images with text overlays using GD Library
 * to provide SEO-optimized images for bulk generated pages.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Rife_PG_Image_Generator {
    
    /**
     * Image dimensions
     */
    private $width = 1200;
    private $height = 630;
    
    /**
     * Font settings
     */
    private $font_size = 48;
    private $font_file;
    
    /**
     * Colors
     */
    private $background_colors = [
        ['r' => 59, 'g' => 130, 'b' => 246],  // Blue
        ['r' => 16, 'g' => 185, 'b' => 129],  // Green
        ['r' => 139, 'g' => 92, 'b' => 246],  // Purple
        ['r' => 245, 'g' => 158, 'b' => 11],  // Orange
        ['r' => 236, 'g' => 72, 'b' => 153],  // Pink
    ];
    
    /**
     * Constructor
     */
    public function __construct() {
        // Set font file path - try to use system fonts first, fallback to default
        $this->font_file = $this->get_font_path();
    }
    
    /**
     * Get available font path
     */
    private function get_font_path() {
        // Try common system fonts
        $possible_fonts = [
            '/usr/share/fonts/truetype/liberation/LiberationSans-Bold.ttf',
            '/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf',
            '/System/Library/Fonts/Helvetica.ttc',
            '/Windows/Fonts/arial.ttf',
            ABSPATH . 'wp-includes/fonts/dashicons.ttf',
        ];
        
        foreach ($possible_fonts as $font) {
            if (file_exists($font)) {
                return $font;
            }
        }
        
        // Fallback to default GD font
        return null;
    }
    
    /**
     * Generate image with text overlay
     */
    public function generate_image($text, $keyword = '', $filename = null) {
        try {
            error_log('Rife PG Image: Starting image generation for text: ' . $text . ', keyword: ' . $keyword);
            
            // Validate input
            if (empty($text)) {
                throw new Exception('Text parameter is empty');
            }
            
            // Check if GD library is available
            if (!extension_loaded('gd')) {
                throw new Exception('GD library is not available on this server');
            }
            
            // Check if uploads directory is writable
            $upload_dir = wp_upload_dir();
            if (!is_writable($upload_dir['path'])) {
                throw new Exception('Uploads directory is not writable: ' . $upload_dir['path']);
            }
            
            // Create image resource
            $image = imagecreatetruecolor($this->width, $this->height);
            if (!$image) {
                throw new Exception('Failed to create image resource');
            }
            
            // Select random background color
            $bg_color = $this->background_colors[array_rand($this->background_colors)];
            $background = imagecolorallocate($image, $bg_color['r'], $bg_color['g'], $bg_color['b']);
            imagefill($image, 0, 0, $background);
            
            // Add gradient effect
            $this->add_gradient($image, $bg_color);
            
            // Add text
            $text_color = imagecolorallocate($image, 255, 255, 255);
            $this->add_text($image, $text, $text_color);
            
            // Add decorative elements
            $this->add_decorative_elements($image, $bg_color);
            
            // Generate filename if not provided
            if (!$filename) {
                $filename = 'rife-pg-' . sanitize_title($keyword ?: $text) . '-' . time() . '.jpg';
            }
            
            $image_path = $upload_dir['path'] . '/' . $filename;
            $image_url = $upload_dir['url'] . '/' . $filename;
            
            // Save image
            $saved = imagejpeg($image, $image_path, 90);
            if (!$saved) {
                throw new Exception('Failed to save image to: ' . $image_path);
            }
            
            // Clean up
            imagedestroy($image);
            
            error_log('Rife PG Image: Image generated successfully: ' . $image_url);
            
            return [
                'success' => true,
                'path' => $image_path,
                'url' => $image_url,
                'filename' => $filename,
                'alt_text' => $this->generate_alt_text($text, $keyword)
            ];
            
        } catch (Exception $e) {
            error_log('Rife PG Image: Generation error - ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Add gradient effect to background
     */
    private function add_gradient($image, $base_color) {
        // Create darker version for gradient
        $darker_color = imagecolorallocate(
            $image,
            max(0, min(255, $base_color['r'] - 30)),
            max(0, min(255, $base_color['g'] - 30)),
            max(0, min(255, $base_color['b'] - 30))
        );
        
        // Add gradient from top to bottom
        for ($y = 0; $y < $this->height; $y++) {
            $alpha = 1 - ($y / $this->height);
            $color = imagecolorallocatealpha(
                $image,
                max(0, min(255, (int)($base_color['r'] * $alpha + ($base_color['r'] - 30) * (1 - $alpha)))),
                max(0, min(255, (int)($base_color['g'] * $alpha + ($base_color['g'] - 30) * (1 - $alpha)))),
                max(0, min(255, (int)($base_color['b'] * $alpha + ($base_color['b'] - 30) * (1 - $alpha))))
            );
            
            imageline($image, 0, $y, $this->width, $y, $color);
        }
    }
    
    /**
     * Add text to image
     */
    private function add_text($image, $text, $text_color) {
        // Clean and truncate text
        $text = sanitize_text_field($text);
        $text = $this->truncate_text($text, 50); // Max 50 characters
        
        // Calculate text positioning
        $font_size = $this->font_size;
        $text_box = $this->calculate_text_box($text, $font_size);
        
        $x = ($this->width - $text_box['width']) / 2;
        $y = ($this->height - $text_box['height']) / 2 + $text_box['height'];
        
        // Add text shadow
        $shadow_color = imagecolorallocate($image, 0, 0, 0);
        $this->add_text_with_shadow($image, $text, $x, $y, $font_size, $text_color, $shadow_color);
    }
    
    /**
     * Add text with shadow effect
     */
    private function add_text_with_shadow($image, $text, $x, $y, $font_size, $text_color, $shadow_color) {
        if ($this->font_file && file_exists($this->font_file)) {
            // Use TrueType font if available
            imagettftext($image, $font_size, 0, $x + 2, $y + 2, $shadow_color, $this->font_file, $text);
            imagettftext($image, $font_size, 0, $x, $y, $text_color, $this->font_file, $text);
        } else {
            // Fallback to default GD font
            $font = 5; // Largest built-in font
            imagestring($image, $font, $x + 1, $y + 1, $text, $shadow_color);
            imagestring($image, $font, $x, $y, $text, $text_color);
        }
    }
    
    /**
     * Calculate text bounding box
     */
    private function calculate_text_box($text, $font_size) {
        if ($this->font_file && file_exists($this->font_file)) {
            $bbox = imagettfbbox($font_size, 0, $this->font_file, $text);
            return [
                'width' => abs($bbox[4] - $bbox[0]),
                'height' => abs($bbox[5] - $bbox[1])
            ];
        } else {
            // Estimate for default font
            $width = strlen($text) * 8;
            $height = 16;
            return ['width' => $width, 'height' => $height];
        }
    }
    
    /**
     * Add decorative elements
     */
    private function add_decorative_elements($image, $base_color) {
        // Add some geometric shapes
        $shape_color = imagecolorallocatealpha(
            $image,
            255,
            255,
            255,
            30 // Transparency
        );
        
        // Add circles or rectangles as decorative elements
        for ($i = 0; $i < 3; $i++) {
            $x = rand(50, $this->width - 50);
            $y = rand(50, $this->height - 50);
            $size = rand(20, 60);
            
            if (rand(0, 1)) {
                imagefilledellipse($image, $x, $y, $size, $size, $shape_color);
            } else {
                imagefilledrectangle($image, $x, $y, $x + $size, $y + $size, $shape_color);
            }
        }
    }
    
    /**
     * Truncate text to fit image
     */
    private function truncate_text($text, $max_length) {
        if (strlen($text) <= $max_length) {
            return $text;
        }
        return substr($text, 0, $max_length - 3) . '...';
    }
    
    /**
     * Generate ALT text for image
     */
    private function generate_alt_text($text, $keyword) {
        if ($keyword) {
            return sanitize_text_field($keyword . ' services - ' . $text);
        }
        return sanitize_text_field('Professional services - ' . $text);
    }
    
    /**
     * Generate multiple images for different sections
     */
    public function generate_image_set($keyword, $section_type = 'general') {
        $images = [];
        
        // Generate different types of images based on section
        $texts = $this->get_section_texts($keyword, $section_type);
        
        foreach ($texts as $index => $text) {
            $filename = 'rife-pg-' . sanitize_title($section_type) . '-' . $index . '-' . time() . '.jpg';
            $result = $this->generate_image($text, $keyword, $filename);
            
            if ($result['success']) {
                $images[] = $result;
            }
        }
        
        return $images;
    }
    
    /**
     * Get appropriate text for different sections
     */
    private function get_section_texts($keyword, $section_type) {
        $texts = [];
        
        switch ($section_type) {
            case 'hero':
                $texts = [
                    $keyword . ' Excellence',
                    'Professional ' . $keyword,
                    $keyword . ' Solutions'
                ];
                break;
            case 'benefits':
                $texts = [
                    'Why Choose ' . $keyword,
                    $keyword . ' Benefits',
                    'Our ' . $keyword . ' Advantage'
                ];
                break;
            case 'process':
                $texts = [
                    $keyword . ' Process',
                    'How ' . $keyword . ' Works',
                    $keyword . ' Methodology'
                ];
                break;
            case 'testimonials':
                $texts = [
                    'Client Success',
                    'Happy Customers',
                    'Proven Results'
                ];
                break;
            default:
                $texts = [
                    $keyword . ' Services',
                    'Professional ' . $keyword,
                    'Expert ' . $keyword
                ];
        }
        
        return $texts;
    }
    
    /**
     * Set image as featured image for post
     */
    public function set_featured_image($post_id, $image_path, $image_url, $alt_text) {
        try {
            error_log('Rife PG Image: Setting featured image for post ' . $post_id . ' from: ' . $image_path);
            
            // Validate input
            if (empty($post_id) || empty($image_path) || empty($image_url)) {
                throw new Exception('Missing required parameters for featured image');
            }
            
            // Check if file exists
            if (!file_exists($image_path)) {
                throw new Exception('Image file does not exist: ' . $image_path);
            }
            
            require_once(ABSPATH . 'wp-admin/includes/image.php');
            require_once(ABSPATH . 'wp-admin/includes/file.php');
            require_once(ABSPATH . 'wp-admin/includes/media.php');
            
            // Check if image already exists in media library
            $existing_image = $this->find_existing_image($image_url);
            if ($existing_image) {
                error_log('Rife PG Image: Using existing image: ' . $existing_image);
                return set_post_thumbnail($post_id, $existing_image);
            }
            
            // Upload image to media library
            $attachment = [
                'post_mime_type' => 'image/jpeg',
                'post_title' => sanitize_file_name(basename($image_path)),
                'post_content' => '',
                'post_status' => 'inherit'
            ];
            
            $attach_id = wp_insert_attachment($attachment, $image_path, $post_id);
            
            if (is_wp_error($attach_id)) {
                throw new Exception('Failed to create attachment: ' . $attach_id->get_error_message());
            }
            
            // Generate metadata and update attachment
            $attach_data = wp_generate_attachment_metadata($attach_id, $image_path);
            if (is_wp_error($attach_data)) {
                throw new Exception('Failed to generate attachment metadata: ' . $attach_data->get_error_message());
            }
            
            $updated = wp_update_attachment_metadata($attach_id, $attach_data);
            if (!$updated) {
                error_log('Rife PG Image: Warning - Failed to update attachment metadata');
            }
            
            // Set ALT text
            $alt_updated = update_post_meta($attach_id, '_wp_attachment_image_alt', $alt_text);
            if (!$alt_updated) {
                error_log('Rife PG Image: Warning - Failed to set ALT text');
            }
            
            // Set as featured image
            $result = set_post_thumbnail($post_id, $attach_id);
            if ($result) {
                error_log('Rife PG Image: Successfully set featured image for post ' . $post_id);
            } else {
                error_log('Rife PG Image: Failed to set featured image for post ' . $post_id);
            }
            
            return $result;
            
        } catch (Exception $e) {
            error_log('Rife PG Image: Featured image error - ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Find existing image in media library
     */
    private function find_existing_image($image_url) {
        global $wpdb;
        
        $filename = basename($image_url);
        $query = $wpdb->prepare(
            "SELECT post_id FROM {$wpdb->postmeta} 
             WHERE meta_key = '_wp_attached_file' 
             AND meta_value LIKE %s",
            '%' . $filename
        );
        
        $existing_id = $wpdb->get_var($query);
        return $existing_id ? $existing_id : false;
    }
}
?>