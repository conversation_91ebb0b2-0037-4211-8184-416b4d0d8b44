<?php
/**
 * Page Generator Admin View
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get the template manager and form builder from the main plugin instance
$template_manager = rife_pg()->template_manager;
$form_builder = new Rife_PG_Form_Builder();
$templates = $template_manager->get_available_templates();

// Get the selected template from URL, if any
$selected_template_id = isset($_GET['template']) ? sanitize_text_field($_GET['template']) : '';

$template_config = null;
if ($selected_template_id) {
    $template_config_path = RIFE_PG_PLUGIN_DIR . 'templates/' . $selected_template_id . '/config.php';
    if (file_exists($template_config_path)) {
        $template_config = include $template_config_path;
    }
}

?>

<div class="wrap rife-pg-admin">
    <div class="rife-pg-header">
        <h1 class="wp-heading-inline">
            <?php echo 'Page Generator'; ?>
        </h1>
        <p class="description">
            <?php echo 'Create your landing page by selecting a template, customizing the content, and styling it to match your brand.'; ?>
        </p>
    </div>

    <div class="rife-pg-layout">
        <!-- Main Content Area -->
        <form id="page-generator-form" class="rife-pg-main-content" method="POST" action="<?php echo admin_url('admin-post.php'); ?>">
            <?php wp_nonce_field('rife_pg_generate', 'rife_pg_nonce'); ?>
            <input type="hidden" name="action" value="rife_pg_generate_page">
            <input type="hidden" name="template_id" value="<?php echo esc_attr($selected_template_id); ?>">

            <div class="content-header">
                <h2><?php echo 'Add Content'; ?></h2>
                <p><?php echo 'Fill in the content for each section of your landing page.'; ?></p>
            </div>

            <div class="content-input" id="content-sections">
                <div class="content-sections-container">
                    <?php
                    if ($template_config) {
                        $form_builder->render_form($template_config);
                    } else {
                        echo '<div class="no-template-selected"><p>' . esc_html('Please select a template from the sidebar to begin.') . '</p></div>';
                    }
                    ?>
                </div>
            </div>
        </form>

        <!-- Sidebar -->
        <div class="rife-pg-sidebar">
            <!-- Template Selection Card -->
            <div class="sidebar-card template-card">
                <div class="card-header">
                    <h3><i class="dashicons dashicons-layout"></i> <?php echo 'Choose Template'; ?></h3>
                </div>
                <div class="card-content">
                    <form id="template-selection-form" method="GET">
                        <input type="hidden" name="page" value="rife-pg-generator">
                        <div class="template-selector">
                            <select name="template" required>
                                <option value=""><?php echo 'Select a template...'; ?></option>
                                <?php foreach ($templates as $template): ?>
                                    <option value="<?php echo esc_attr($template['id']); ?>"
                                            <?php selected($selected_template_id, $template['id']); ?>>
                                        <?php echo esc_html($template['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <button type="submit" class="button button-secondary">
                                <?php echo 'Load Template'; ?>
                            </button>
                        </div>
                    </form>

                    <!-- Add Dummy Content Button -->
                    <?php if ($selected_template_id): ?>
                    <div class="template-actions" style="margin-top: 16px; padding-top: 16px; border-top: 1px solid #e2e8f0;">
                        <button type="button" class="btn btn-outline" id="fill-dummy-content"
                                data-template-id="<?php echo esc_attr($selected_template_id); ?>">
                            <i class="dashicons dashicons-admin-page"></i>
                            <?php echo 'Fill Dummy Content'; ?>
                        </button>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Bulk Generate Card -->
            <div class="sidebar-card bulk-generate-card">
                <div class="card-header">
                    <h3><i class="dashicons dashicons-upload"></i> <?php echo 'Bulk Generate'; ?></h3>
                </div>
                <div class="card-content">
                    <p class="description" style="margin-bottom: 12px;">
                        <?php echo 'Generate multiple pages from a JSON file. Styles set above will be applied to all pages.'; ?>
                    </p>
                    <div class="bulk-controls">
                        <div class="control-group">
                            <label for="bulk-json-file"><?php echo 'JSON File'; ?></label>
                            <input type="file" id="bulk-json-file" accept=".json">
                             <p class="description">
                                <a href="<?php echo RIFE_PG_PLUGIN_URL . 'admin/views/example.json'; ?>" download>Unduh contoh file JSON</a> atau
                                <a href="<?php echo RIFE_PG_PLUGIN_URL . 'admin/views/example-with-yoast.json'; ?>" download>contoh dengan Yoast SEO</a> atau
                                <a href="<?php echo RIFE_PG_PLUGIN_URL . 'admin/views/example-with-images.json'; ?>" download>contoh dengan gambar & SEO</a>.
                            </p>
                        </div>
                        <div class="generation-options">
                            <label class="checkbox-label">
                                <input type="checkbox" id="auto_publish_bulk" value="1">
                                <span><?php echo 'Publish immediately'; ?></span>
                            </label>
                        </div>
                        <button type="button" class="btn btn-outline" id="bulk-generate-button" style="width: 100%; margin-top: 10px; justify-content: center;">
                            <i class="dashicons dashicons-database-add"></i>
                            <span><?php echo 'Bulk Generate Pages'; ?></span>
                        </button>
                    </div>
                    <div id="bulk-status-message" style="margin-top: 15px;"></div>
                </div>
            </div>

            <!-- Design Settings Card -->
            <div class="sidebar-card design-card">
                <div class="card-header">
                    <h3><i class="dashicons dashicons-admin-appearance"></i> <?php echo 'Design Settings'; ?></h3>
                </div>
                <div class="card-content">
                    <div class="design-controls">
                        <div class="control-grid">
                            <div class="control-group">
                                <label for="primary_color"><?php echo 'Primary Color'; ?></label>
                                <input type="color" id="primary_color" name="primary_color" value="#4f46e5">
                            </div>
                            <div class="control-group">
                                <label for="secondary_color"><?php echo 'Secondary Color'; ?></label>
                                <input type="color" id="secondary_color" name="secondary_color" value="#64748b">
                            </div>
                            <div class="control-group">
                                <label for="accent_color"><?php echo 'Accent Color'; ?></label>
                                <input type="color" id="accent_color" name="accent_color" value="#10b981">
                            </div>
                            <div class="control-group">
                                <label for="text_color"><?php echo 'Text Color'; ?></label>
                                <input type="color" id="text_color" name="text_color" value="#1e293b">
                            </div>
                            <div class="control-group">
                                <label for="background_color"><?php echo 'Background Color'; ?></label>
                                <input type="color" id="background_color" name="background_color" value="#ffffff">
                            </div>
                        </div>

                        <hr>

                        <h4><?php echo 'Typography'; ?></h4>
                        <div class="control-group-text">
                            <div class="control-group">
                                <label for="heading_font"><?php echo 'Heading Font'; ?></label>
                                <select id="heading_font" name="heading_font">
                                    <option value="Inter" selected>Inter</option>
                                    <option value="Roboto">Roboto</option>
                                    <option value="Open Sans">Open Sans</option>
                                    <option value="Lato">Lato</option>
                                    <option value="Montserrat">Montserrat</option>
                                    <option value="Poppins">Poppins</option>
                                    <option value="Nunito">Nunito</option>
                                    <option value="Playfair Display">Playfair Display</option>
                                </select>
                            </div>
                            <div class="control-group">
                                <label for="body_font"><?php echo 'Body Font'; ?></label>
                                <select id="body_font" name="body_font">
                                    <option value="Inter" selected>Inter</option>
                                    <option value="Roboto">Roboto</option>
                                    <option value="Open Sans">Open Sans</option>
                                    <option value="Lato">Lato</option>
                                    <option value="Montserrat">Montserrat</option>
                                    <option value="Poppins">Poppins</option>
                                    <option value="Nunito">Nunito</option>
                                </select>
                            </div>
                            <div class="control-group">
                                <label for="base_font_size"><?php echo 'Base Font Size'; ?></label>
                                <select id="base_font_size" name="base_font_size">
                                    <option value="14px">Small (14px)</option>
                                    <option value="16px" selected>Medium (16px)</option>
                                    <option value="18px">Large (18px)</option>
                                    <option value="20px">Extra Large (20px)</option>
                                </select>
                            </div>
                        </div>

                        <hr>

                        <h4><?php echo 'Spacing'; ?></h4>
                        <div class="control-group-text">
                            <div class="control-group">
                                <label for="section_padding"><?php echo 'Section Padding'; ?></label>
                                <select id="section_padding" name="section_padding">
                                    <option value="40px 0">Small (40px)</option>
                                    <option value="60px 0">Medium (60px)</option>
                                    <option value="80px 0" selected>Large (80px)</option>
                                    <option value="100px 0">Extra Large (100px)</option>
                                </select>
                            </div>
                            <div class="control-group">
                                <label for="element_spacing"><?php echo 'Element Spacing'; ?></label>
                                <select id="element_spacing" name="element_spacing">
                                    <option value="16px">Tight (16px)</option>
                                    <option value="24px" selected>Normal (24px)</option>
                                    <option value="32px">Loose (32px)</option>
                                    <option value="40px">Extra Loose (40px)</option>
                                </select>
                            </div>
                            <div class="control-group">
                                <label for="border_radius"><?php echo 'Border Radius'; ?></label>
                                <select id="border_radius" name="border_radius">
                                    <option value="0px">None (0px)</option>
                                    <option value="4px">Small (4px)</option>
                                    <option value="8px" selected>Medium (8px)</option>
                                    <option value="12px">Large (12px)</option>
                                    <option value="16px">Extra Large (16px)</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Generate Page Card -->
            <div class="sidebar-card generate-card">
                <div class="card-header">
                    <h3><i class="dashicons dashicons-admin-tools"></i> <?php echo 'Generate Page'; ?></h3>
                </div>
                <div class="card-content">
                    <div class="generation-options">
                        <label class="checkbox-label">
                            <input type="checkbox" id="auto_publish_single" name="auto_publish" value="1" form="page-generator-form">
                            <span><?php echo 'Publish immediately'; ?></span>
                        </label>
                    </div>

                    <div class="generation-buttons">
                        <button type="submit" form="page-generator-form" class="btn btn-primary btn-hero" id="generate-page">
                            <i class="dashicons dashicons-plus-alt"></i>
                            <?php echo 'Generate Page'; ?>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Floating Scroll Buttons -->
<div class="rife-floating-scroll">
    <button class="scroll-to-top-btn" id="scroll-to-top" title="Scroll ke atas">
        <i class="dashicons dashicons-arrow-up-alt2"></i>
    </button>
    <button class="scroll-to-bottom-btn" id="scroll-to-bottom" title="Scroll ke bawah">
        <i class="dashicons dashicons-arrow-down-alt2"></i>
    </button>
</div>

<script>
jQuery(document).ready(function($) {
    'use strict';

    // Fill Dummy Content functionality
    $('#fill-dummy-content').on('click', function() {
        var button = $(this);
        var templateId = button.data('template-id');

        if (!templateId) {
            alert('<?php echo 'No template selected'; ?>');
            return;
        }

        // Show loading state
        button.prop('disabled', true);
        var originalText = button.html();
        button.html('<i class="dashicons dashicons-update spin"></i> <?php echo 'Loading...'; ?>');

        // Make AJAX request to get dummy content
        $.ajax({
            url: rifePgAdmin.ajaxUrl,
            type: 'POST',
            data: {
                action: 'rife_pg_get_dummy_content',
                template_id: templateId,
                nonce: rifePgAdmin.nonce
            },
            success: function(response) {
                console.log('Dummy content response:', response); // Debug log
                if (response.success && response.data && response.data.dummy_content) {
                    fillFormWithDummyContent(response.data.dummy_content);

                    // Trigger masonry layout update
                    $(document).trigger('template-loaded');

                    // Show success message
                    button.html('<i class="dashicons dashicons-yes"></i> <?php echo 'Content Filled!'; ?>');
                    setTimeout(function() {
                        button.html(originalText);
                        button.prop('disabled', false);
                    }, 2000);
                } else {
                    console.error('Invalid response structure:', response);
                    alert('<?php echo 'Failed to load dummy content: '; ?>' + (response.data || 'Invalid response structure'));
                    button.html(originalText);
                    button.prop('disabled', false);
                }
            },
            error: function(xhr, status, error) {
                console.error('Dummy content AJAX error:', xhr, status, error); // Debug log
                alert('<?php echo 'Failed to load dummy content'; ?>');
                button.html(originalText);
                button.prop('disabled', false);
            }
        });
    });

    // Function to fill form with dummy content
    function fillFormWithDummyContent(dummyContent) {
        console.log('DEBUG: fillFormWithDummyContent called with:', dummyContent);
        var fieldsFound = 0;
        var fieldsTotal = 0;

        // Recursive function to fill nested content
        function fillNestedContent(data, prefix) {
            console.log('DEBUG: fillNestedContent called with prefix:', prefix, 'data:', data);
            $.each(data, function(key, value) {
                console.log('DEBUG: Processing key:', key, 'value:', value, 'type:', typeof value);
                fieldsTotal++;

                if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
                    // If it's a nested object, recurse
                    console.log('DEBUG: Recursing into nested object for key:', key);
                    fillNestedContent(value, prefix + '[' + key + ']');
                } else {
                    // It's a value, try to fill the form field
                    var formFieldName = prefix + '[' + key + ']';
                    console.log('DEBUG: Looking for field:', formFieldName, 'with value:', value);
                    var field = $('[name="' + formFieldName + '"]');

                    if (field.length > 0) {
                        console.log('DEBUG: Field found:', formFieldName, 'field type:', field.prop('type'));
                        if (field.is('input[type="text"], input[type="url"], input[type="email"], input[type="tel"]')) {
                            field.val(value);
                            fieldsFound++;
                            console.log('DEBUG: Filled text input:', formFieldName, 'with:', value);
                        } else if (field.is('textarea')) {
                            field.val(value);
                            fieldsFound++;
                            console.log('DEBUG: Filled textarea:', formFieldName, 'with:', value);
                        } else if (field.is('input[type="color"]')) {
                            field.val(value);
                            fieldsFound++;
                            console.log('DEBUG: Filled color input:', formFieldName, 'with:', value);
                        } else if (field.is('select')) {
                            field.val(value);
                            fieldsFound++;
                            console.log('DEBUG: Filled select:', formFieldName, 'with:', value);
                        } else {
                            console.log('DEBUG: Field found but type not handled:', formFieldName, 'type:', field.prop('type'));
                        }

                        // Add visual feedback
                        field.addClass('field-filled');
                        setTimeout(function() {
                            field.removeClass('field-filled');
                        }, 1000);
                    } else {
                        console.log('DEBUG: Field not found:', formFieldName, 'for key:', key);
                    }
                }
            });
        }

        // Handle top-level design settings (not nested)
        var designSettings = ['primary_color', 'secondary_color', 'accent_color', 'text_color', 'background_color', 'section_padding', 'border_radius'];
        $.each(designSettings, function(index, settingName) {
            if (dummyContent[settingName]) {
                fieldsTotal++;
                var field = $('[name="' + settingName + '"]');
                if (field.length > 0) {
                    field.val(dummyContent[settingName]);
                    fieldsFound++;

                    // Add visual feedback
                    field.addClass('field-filled');
                    setTimeout(function() {
                        field.removeClass('field-filled');
                    }, 1000);
                } else {
                    console.log('Field not found:', settingName, 'for', settingName);
                }
            }
        });

        // Handle trust badges (not nested)
        var trustBadges = ['trust_badge_1_text', 'trust_badge_2_text', 'trust_badge_3_text', 'trust_badge_4_text', 'trust_badge_5_text'];
        $.each(trustBadges, function(index, badgeName) {
            if (dummyContent[badgeName]) {
                fieldsTotal++;
                var field = $('[name="' + badgeName + '"]');
                if (field.length > 0) {
                    field.val(dummyContent[badgeName]);
                    fieldsFound++;

                    // Add visual feedback
                    field.addClass('field-filled');
                    setTimeout(function() {
                        field.removeClass('field-filled');
                    }, 1000);
                } else {
                    console.log('Field not found:', badgeName, 'for', badgeName);
                }
            }
        });

        // Handle nested content structure
        if (dummyContent.content) {
            fillNestedContent(dummyContent.content, 'content');
        }

        console.log('Fields filled:', fieldsFound, 'out of', fieldsTotal);

        // Show success notification
        showNotification('<?php echo 'Dummy content has been filled successfully!'; ?> (' + fieldsFound + ' fields)', 'success');
    }

    // Simple notification function
    function showNotification(message, type) {
        var notification = $('<div class="rife-notification rife-notification-' + type + '">' + message + '</div>');
        $('body').append(notification);

        notification.fadeIn(300);
        setTimeout(function() {
            notification.fadeOut(300, function() {
                notification.remove();
            });
        }, 3000);
    }

    // Floating scroll button functionality
    $('#scroll-to-bottom').on('click', function() {
        // Scroll ke bagian paling bawah dari konten
        var $target = $('.rife-pg-layout');
        if ($target.length) {
            $target[0].scrollIntoView({
                behavior: 'smooth',
                block: 'end'
            });
        } else {
            // Fallback: scroll ke bagian paling bawah halaman
            window.scrollTo({
                top: document.body.scrollHeight,
                behavior: 'smooth'
            });
        }
        
        // Animasi feedback pada tombol
        var $button = $(this);
        $button.addClass('clicked');
        setTimeout(function() {
            $button.removeClass('clicked');
        }, 300);
    });

    // Floating scroll to top functionality
    $('#scroll-to-top').on('click', function() {
        // Scroll ke bagian paling atas halaman
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
        
        // Animasi feedback pada tombol
        var $button = $(this);
        $button.addClass('clicked');
        setTimeout(function() {
            $button.removeClass('clicked');
        }, 300);
    });

    // Tampilkan/sembunyikan tombol berdasarkan scroll position
    $(window).on('scroll', function() {
        var $scrollBottomButton = $('#scroll-to-bottom');
        var $scrollTopButton = $('#scroll-to-top');
        var scrollTop = $(this).scrollTop();
        var documentHeight = $(document).height();
        var windowHeight = $(this).height();
        
        // Sembunyikan tombol bawah jika sudah di bagian bawah (100px dari bawah)
        if (scrollTop + windowHeight >= documentHeight - 100) {
            $scrollBottomButton.addClass('hidden');
        } else {
            $scrollBottomButton.removeClass('hidden');
        }
        
        // Tampilkan tombol atas jika sudah scroll ke bawah (lebih dari 300px)
        if (scrollTop > 300) {
            $scrollTopButton.removeClass('hidden');
        } else {
            $scrollTopButton.addClass('hidden');
        }
    });

    // Inisialisasi: cek posisi scroll saat halaman dimuat
    $(window).trigger('scroll');
});
</script>

<style>
/* Spinning animation for loading */
.dashicons.spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Field filled animation */
.field-filled {
    background-color: #d1fae5 !important;
    border-color: #10b981 !important;
    transition: all 0.3s ease;
}

/* Notification styles */
.rife-notification {
    position: fixed;
    top: 32px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    z-index: 100000;
    display: none;
}

.rife-notification-success {
    background: #10b981;
}

.rife-notification-error {
    background: #ef4444;
}

/* Floating Scroll Button Styles */
.rife-floating-scroll {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.scroll-to-bottom-btn,
.scroll-to-top-btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #4f46e5;
    color: white;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
    transition: all 0.3s ease;
    font-size: 20px;
}

.scroll-to-bottom-btn:hover,
.scroll-to-top-btn:hover {
    background: #4338ca;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(79, 70, 229, 0.4);
}

.scroll-to-bottom-btn:active,
.scroll-to-top-btn:active {
    transform: translateY(0);
}

.scroll-to-bottom-btn .dashicons,
.scroll-to-top-btn .dashicons {
    width: 24px;
    height: 24px;
    font-size: 24px;
}

/* Animasi saat tombol diklik */
.scroll-to-bottom-btn.clicked,
.scroll-to-top-btn.clicked {
    transform: scale(0.9);
    background: #3730a3;
}

/* Class untuk menyembunyikan tombol */
.scroll-to-bottom-btn.hidden,
.scroll-to-top-btn.hidden {
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
}

/* Transisi smooth untuk show/hide */
.scroll-to-bottom-btn,
.scroll-to-top-btn {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Animasi smooth scroll */
html {
    scroll-behavior: smooth;
}

/* Responsive untuk mobile */
@media (max-width: 768px) {
    .rife-floating-scroll {
        bottom: 20px;
        right: 20px;
    }
    
    .scroll-to-bottom-btn,
    .scroll-to-top-btn {
        width: 45px;
        height: 45px;
        font-size: 18px;
    }
    
    .scroll-to-bottom-btn .dashicons,
    .scroll-to-top-btn .dashicons {
        width: 20px;
        height: 20px;
        font-size: 20px;
    }
}
</style>