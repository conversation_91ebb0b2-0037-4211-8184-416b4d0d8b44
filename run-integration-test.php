<?php
/**
 * Simple test runner for Yoast SEO Integration
 * 
 * This script provides a simple way to test the integration
 * without needing to access WordPress admin.
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

echo "<h1>Rife PageGenerator - Yoast SEO Integration Test Runner</h1>\n";
echo "<p>Testing integration components...</p>\n";

// Test 1: Check if files exist
echo "<h2>File Structure Test</h2>\n";

$required_files = [
    'rife-pagegenerator.php' => 'Main plugin file',
    'includes/class-page-generator.php' => 'Page Generator class',
    'includes/class-ajax-handler.php' => 'AJAX Handler class',
    'modules/class-image-generator.php' => 'Image Generator module',
    'admin/views/example-with-yoast.json' => 'Example JSON with Yoast data',
    'YOAST_SEO_INTEGRATION.md' => 'Integration documentation'
];

$all_files_exist = true;

foreach ($required_files as $file => $description) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✅ $description: <code>$file</code></p>\n";
    } else {
        echo "<p style='color: red;'>❌ $description: <code>$file</code> - FILE NOT FOUND</p>\n";
        $all_files_exist = false;
    }
}

// Test 2: Check JSON structure
echo "<h2>JSON Structure Test</h2>\n";

$json_file = 'admin/views/example-with-yoast.json';
if (file_exists($json_file)) {
    $json_content = file_get_contents($json_file);
    $data = json_decode($json_content, true);
    
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "<p style='color: green;'>✅ JSON is valid</p>\n";
        echo "<p>📊 Contains " . count($data) . " sample pages</p>\n";
        
        // Check first item for Yoast fields
        if (!empty($data[0])) {
            $first_item = $data[0];
            $yoast_fields = [
                'yoast_focus_keyphrase' => 'Focus Keyphrase',
                'yoast_seo_title' => 'SEO Title',
                'yoast_meta_description' => 'Meta Description'
            ];
            
            echo "<h3>Yoast SEO Fields in Sample Data:</h3>\n";
            foreach ($yoast_fields as $field => $label) {
                if (isset($first_item[$field]) && !empty($first_item[$field])) {
                    $value = htmlspecialchars(substr($first_item[$field], 0, 60));
                    echo "<p style='color: green;'>✅ $label: <em>$value...</em></p>\n";
                } else {
                    echo "<p style='color: red;'>❌ $label: Missing or empty</p>\n";
                }
            }
        }
    } else {
        echo "<p style='color: red;'>❌ JSON is invalid: " . json_last_error_msg() . "</p>\n";
    }
} else {
    echo "<p style='color: red;'>❌ JSON file not found</p>\n";
}

// Test 3: Check PHP extensions
echo "<h2>PHP Extensions Test</h2>\n";

$required_extensions = [
    'gd' => 'GD Library (for image generation)',
    'json' => 'JSON support',
    'curl' => 'cURL (for fallback images)'
];

foreach ($required_extensions as $ext => $description) {
    if (extension_loaded($ext)) {
        echo "<p style='color: green;'>✅ $description</p>\n";
        
        if ($ext === 'gd') {
            $gd_info = gd_info();
            echo "<p style='margin-left: 20px;'>📋 GD Version: " . $gd_info['GD Version'] . "</p>\n";
            echo "<p style='margin-left: 20px;'>📋 JPEG Support: " . ($gd_info['JPEG Support'] ? 'Yes' : 'No') . "</p>\n";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ $description - Not available (fallback will be used)</p>\n";
    }
}

// Test 4: Check WordPress constants (if available)
echo "<h2>WordPress Environment Test</h2>\n";

if (defined('ABSPATH')) {
    echo "<p style='color: green;'>✅ WordPress environment detected</p>\n";
    echo "<p>📁 ABSPATH: " . ABSPATH . "</p>\n";
    
    if (function_exists('wp_insert_post')) {
        echo "<p style='color: green;'>✅ WordPress functions available</p>\n";
    } else {
        echo "<p style='color: orange;'>⚠️ WordPress functions not loaded</p>\n";
    }
} else {
    echo "<p style='color: orange;'>⚠️ WordPress environment not detected (running standalone)</p>\n";
}

// Test 5: Check file permissions
echo "<h2>File Permissions Test</h2>\n";

if (function_exists('wp_upload_dir')) {
    $upload_dir = wp_upload_dir();
    $upload_path = $upload_dir['path'];
    if (is_writable($upload_path)) {
        echo "<p style='color: green;'>✅ Upload directory is writable: $upload_path</p>\n";
    } else {
        echo "<p style='color: red;'>❌ Upload directory is not writable: $upload_path</p>\n";
    }
} else {
    // Fallback check
    $temp_dir = sys_get_temp_dir();
    if (is_writable($temp_dir)) {
        echo "<p style='color: green;'>✅ System temp directory is writable: $temp_dir</p>\n";
    } else {
        echo "<p style='color: red;'>❌ System temp directory is not writable: $temp_dir</p>\n";
    }
}

// Summary
echo "<h2>Test Summary</h2>\n";

if ($all_files_exist) {
    echo "<p style='color: green; font-weight: bold; font-size: 18px;'>🎉 All core files are present!</p>\n";
    echo "<p>The Yoast SEO integration should work correctly. You can now:</p>\n";
    echo "<ul>\n";
    echo "<li>Upload the example JSON file through WordPress admin</li>\n";
    echo "<li>Use the Bulk Generate feature</li>\n";
    echo "<li>Check that generated pages have Yoast SEO metadata</li>\n";
    echo "<li>Verify that featured images are generated and set</li>\n";
    echo "</ul>\n";
} else {
    echo "<p style='color: red; font-weight: bold; font-size: 18px;'>⚠️ Some files are missing!</p>\n";
    echo "<p>Please ensure all required files are present before using the integration.</p>\n";
}

echo "<h2>Next Steps</h2>\n";
echo "<ol>\n";
echo "<li>Ensure Yoast SEO plugin is installed and activated in WordPress</li>\n";
echo "<li>Access WordPress admin and go to Rife PageGenerator</li>\n";
echo "<li>Use the Bulk Generate feature with the example JSON file</li>\n";
echo "<li>Check generated pages for Yoast SEO metadata and featured images</li>\n";
echo "<li>Monitor error logs for any issues</li>\n";
echo "</ol>\n";

echo "<hr>\n";
echo "<p><small>Test completed at " . date('Y-m-d H:i:s') . "</small></p>\n";
?>
