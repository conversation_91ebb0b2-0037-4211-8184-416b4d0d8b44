<?php
/**
 * <PERSON><PERSON><PERSON> Handler for Rife PageGenerator
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Rife_PG_Ajax_Handler {
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Template preview
        add_action('wp_ajax_rife_pg_preview_template', array($this, 'preview_template'));
        
        // Page generation
        add_action('wp_ajax_rife_pg_generate_page', array($this, 'generate_page'));
        add_action('wp_ajax_rife_pg_bulk_generate_ajax', array($this, 'bulk_generate_ajax'));
        add_action('admin_post_rife_pg_generate_page', array($this, 'handle_admin_post_generate_page'));
        
        // Page management
        add_action('wp_ajax_rife_pg_delete_page', array($this, 'delete_page'));
        add_action('wp_ajax_rife_pg_duplicate_page', array($this, 'duplicate_page'));
        
        // Settings
        add_action('wp_ajax_rife_pg_reset_settings', array($this, 'reset_settings'));
        add_action('wp_ajax_rife_pg_clear_cache', array($this, 'clear_cache'));
        
        // Style customizer
        add_action('wp_ajax_rife_pg_save_styles', array($this, 'save_styles'));
        add_action('wp_ajax_rife_pg_load_styles', array($this, 'load_styles'));
        
        // Template management
        add_action('wp_ajax_rife_pg_get_template_data', array($this, 'get_template_data'));
        add_action('wp_ajax_rife_pg_validate_template', array($this, 'validate_template'));
        add_action('wp_ajax_rife_pg_get_dummy_content', array($this, 'get_dummy_content'));
    }

    /**
     * Bulk Generate Pages via AJAX
     */
    public function bulk_generate_ajax() {
        try {
            // Enable error reporting for debugging
            ini_set('display_errors', 1);
            ini_set('log_errors', 1);
            error_reporting(E_ALL);
            
            error_log('Rife PG: === BULK GENERATE AJAX STARTED ===');
            
            require_once RIFE_PG_PLUGIN_DIR . 'includes/class-page-generator.php';

            // Debug logging
            error_log('Rife PG: Bulk generate AJAX started');
            error_log('Rife PG: POST data: ' . print_r($_POST, true));
            error_log('Rife PG: FILES data: ' . print_r($_FILES, true));
            error_log('Rife PG: PHP version: ' . phpversion());
            error_log('Rife PG: GD extension loaded: ' . (extension_loaded('gd') ? 'Yes' : 'No'));
            error_log('Rife PG: Memory limit: ' . ini_get('memory_limit'));
            error_log('Rife PG: Max execution time: ' . ini_get('max_execution_time'));
            error_log('Rife PG: Upload max filesize: ' . ini_get('upload_max_filesize'));
            error_log('Rife PG: Post max size: ' . ini_get('post_max_size'));

            // Security checks
            if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'rife_pg_generate')) {
                error_log('Rife PG: Nonce verification failed');
                wp_send_json_error('Security check failed.', 403);
            }
            if (!current_user_can('manage_options')) {
                error_log('Rife PG: Permission check failed');
                wp_send_json_error('You do not have permission to perform this action.', 403);
            }

            // File upload check
            if (!isset($_FILES['bulk_file'])) {
                error_log('Rife PG: No bulk_file in FILES array');
                wp_send_json_error('No file uploaded.', 400);
            }
            
            if ($_FILES['bulk_file']['error'] !== UPLOAD_ERR_OK) {
                $error_code = $_FILES['bulk_file']['error'] ?? 'No file';
                error_log('Rife PG: File upload error - ' . $error_code);
                
                // Human readable error messages
                $error_messages = array(
                    UPLOAD_ERR_INI_SIZE => 'The uploaded file exceeds the upload_max_filesize directive in php.ini',
                    UPLOAD_ERR_FORM_SIZE => 'The uploaded file exceeds the MAX_FILE_SIZE directive that was specified in the HTML form',
                    UPLOAD_ERR_PARTIAL => 'The uploaded file was only partially uploaded',
                    UPLOAD_ERR_NO_FILE => 'No file was uploaded',
                    UPLOAD_ERR_NO_TMP_DIR => 'Missing a temporary folder',
                    UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk',
                    UPLOAD_ERR_EXTENSION => 'File upload stopped by extension'
                );
                
                $error_message = isset($error_messages[$error_code]) ? $error_messages[$error_code] : 'Unknown upload error';
                wp_send_json_error('File upload error: ' . $error_message . ' (Error code: ' . $error_code . ')', 400);
            }

            // Read and decode JSON file
            $file_path = $_FILES['bulk_file']['tmp_name'];
            error_log('Rife PG: Reading file from: ' . $file_path);
            
            // Check if file exists and is readable
            if (!file_exists($file_path)) {
                error_log('Rife PG: File does not exist: ' . $file_path);
                wp_send_json_error('Uploaded file does not exist on server.', 400);
            }
            
            if (!is_readable($file_path)) {
                error_log('Rife PG: File is not readable: ' . $file_path);
                wp_send_json_error('Uploaded file is not readable.', 400);
            }
            
            $content = file_get_contents($file_path);
            if ($content === false) {
                error_log('Rife PG: Failed to read file contents');
                wp_send_json_error('Failed to read uploaded file.', 400);
            }
            
            error_log('Rife PG: File contents length: ' . strlen($content));
            
            // Check if content is empty
            if (empty(trim($content))) {
                error_log('Rife PG: File content is empty');
                wp_send_json_error('Uploaded file is empty.', 400);
            }
            
            $json_data = json_decode($content, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                $json_error = json_last_error_msg();
                error_log('Rife PG: JSON decode error - ' . $json_error);
                error_log('Rife PG: Content preview: ' . substr($content, 0, 200));
                wp_send_json_error('Invalid JSON file structure: ' . $json_error, 400);
            }
            
            error_log('Rife PG: JSON decoded successfully, items count: ' . count($json_data));

            // Get data from POST
            $template_id = sanitize_text_field($_POST['template_id'] ?? '');
            $auto_publish = (isset($_POST['auto_publish']) && $_POST['auto_publish'] === 'true');
            $styles = isset($_POST['styles']) ? json_decode(stripslashes($_POST['styles']), true) : array();

            error_log('Rife PG: Template ID: ' . $template_id);
            error_log('Rife PG: Auto publish: ' . ($auto_publish ? 'true' : 'false'));
            error_log('Rife PG: Styles count: ' . count($styles));

            if (empty($template_id)) {
                error_log('Rife PG: Template ID is missing');
                wp_send_json_error('Template ID is missing.', 400);
            }

            // Validate template exists
            $template_path = RIFE_PG_PLUGIN_DIR . 'templates/' . $template_id . '/template.php';
            if (!file_exists($template_path)) {
                error_log('Rife PG: Template file not found: ' . $template_path);
                wp_send_json_error('Template not found: ' . $template_id, 400);
            }

            $page_generator = new Rife_PG_Page_Generator();
            $success_count = 0;
            $error_count = 0;
            $errors = [];

            if (is_array($json_data)) {
                error_log('Rife PG: Processing ' . count($json_data) . ' items from JSON');
                
                foreach ($json_data as $index => $page_content_data) {
                    try {
                        error_log('Rife PG: Processing item ' . ($index + 1));
                        error_log('Rife PG: Item data keys: ' . print_r(array_keys($page_content_data), true));
                        
                        if (empty($page_content_data['hero_title'])) {
                            error_log('Rife PG: Skipping item ' . ($index + 1) . ' - missing hero_title');
                            $error_count++;
                            $errors[] = 'Item ' . ($index + 1) . ': Missing hero_title';
                            continue;
                        }

                        // Validate required fields
                        if (empty($page_content_data['yoast_focus_keyphrase'])) {
                            error_log('Rife PG: Item ' . ($index + 1) . ' missing yoast_focus_keyphrase, will skip image generation');
                        }

                        // Merge content from JSON with styles from the form
                        $merged_data = array_merge($page_content_data, $styles);
                        error_log('Rife PG: Merged data for item ' . ($index + 1));

                        $result = $page_generator->generate_page_simple($template_id, $merged_data, $auto_publish);
                        if ($result['success']) {
                            $success_count++;
                            error_log('Rife PG: Successfully generated page ' . $result['page_id'] . ' for item ' . ($index + 1));
                        } else {
                            error_log('Rife PG Bulk Error for item ' . ($index + 1) . ': ' . $result['message']);
                            $error_count++;
                            $errors[] = 'Item ' . ($index + 1) . ': ' . $result['message'];
                        }
                    } catch (Exception $e) {
                        error_log('Rife PG Exception for item ' . ($index + 1) . ': ' . $e->getMessage());
                        error_log('Rife PG Exception trace: ' . $e->getTraceAsString());
                        $error_count++;
                        $errors[] = 'Item ' . ($index + 1) . ': ' . $e->getMessage();
                    }
                }
            } else {
                error_log('Rife PG: JSON data is not an array');
                wp_send_json_error('Invalid JSON structure: data must be an array.', 400);
            }

            error_log('Rife PG: Bulk generation complete - Success: ' . $success_count . ', Errors: ' . $error_count);

            wp_send_json_success(array(
                'success_count' => $success_count,
                'error_count' => $error_count,
                'errors' => $errors
            ));
        } catch (Exception $e) {
            error_log('Rife PG: Unhandled exception in bulk_generate_ajax - ' . $e->getMessage());
            error_log('Rife PG: Exception trace: ' . $e->getTraceAsString());
            wp_send_json_error('An internal server error occurred: ' . $e->getMessage(), 500);
        } finally {
            error_log('Rife PG: === BULK GENERATE AJAX FINISHED ===');
        }
    }
    
    /**
     * Preview template
     */
    public function preview_template() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'rife_pg_nonce')) {
            wp_die('Security check failed');
        }
        
        // Check permissions
        if (!current_user_can('edit_pages')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        $template_id = sanitize_text_field($_POST['template_id']);
        
        if (empty($template_id)) {
            wp_send_json_error('Template ID is required');
        }
        
        $template_manager = rife_pg()->template_manager;
        $template = $template_manager->get_template($template_id);
        
        if (!$template) {
            wp_send_json_error('Template not found');
        }
        
        $preview_url = $template['preview_url'];
        
        wp_send_json_success(array(
            'preview_url' => $preview_url,
            'template' => $template
        ));
    }
    
    /**
     * Generate page
     */
    public function generate_page() {
        error_log('Rife PG: generate_page AJAX handler called');
        error_log('Rife PG: POST data: ' . print_r($_POST, true));

        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'rife_pg_generate')) {
            error_log('Rife PG: Nonce verification failed');
            wp_die('Security check failed');
        }

        error_log('Rife PG: Nonce verification passed');
        
        // Check permissions
        if (!current_user_can('edit_pages')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        try {
            // Log incoming data for debugging
            error_log('Rife PG: Generate page request - Template ID: ' . ($_POST['template_id'] ?? 'NOT SET'));
            error_log('Rife PG: Form data: ' . print_r($_POST['form_data'] ?? 'NOT SET', true));
            error_log('Rife PG: Auto publish: ' . ($_POST['auto_publish'] ?? 'NOT SET'));

            $template_id = sanitize_text_field($_POST['template_id'] ?? '');

            // Validate template_id
            if (empty($template_id)) {
                error_log('Rife PG: Template ID is empty or missing');
                wp_send_json_error('Template ID is required');
                return;
            }

            // Get form data from the structured data sent by JavaScript
            $raw_form_data = $_POST['form_data'] ?? array();
            $auto_publish = isset($_POST['auto_publish']) && $_POST['auto_publish'] === 'true';

            error_log('Rife PG: Raw form data received: ' . print_r($raw_form_data, true));

            // Convert the structured form data to the flat format expected by the page generator
            $form_data = $this->convert_structured_form_data($raw_form_data);

            error_log('Rife PG: Form data collected: ' . print_r($form_data, true));

            // Validate required data
            if (empty($template_id)) {
                wp_send_json_error('Template ID is required');
            }

            // Validate hero title (required field)
            if (empty($form_data['hero_title'])) {
                error_log('Rife PG: Hero title validation failed. Form data: ' . print_r($form_data, true));
                wp_send_json_error('Hero title is required. Please fill in the Hero Title field.');
            }

            error_log('Rife PG: Validation passed. Generating page...');

            // Generate page using simple string replacement
            $page_generator = rife_pg()->page_generator;
            $result = $page_generator->generate_page_simple($template_id, $form_data, $auto_publish);
            
            if ($result['success']) {
                wp_send_json_success($result);
            } else {
                wp_send_json_error($result['message']);
            }
            
        } catch (Exception $e) {
            error_log('Rife PG: Page generation error - ' . $e->getMessage());
            wp_send_json_error('An error occurred while generating the page');
        }
    }
    
    /**
     * Delete page
     */
    public function delete_page() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'rife_pg_nonce')) {
            wp_die('Security check failed');
        }
        
        // Check permissions
        if (!current_user_can('delete_pages')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        $page_id = intval($_POST['page_id']);
        
        if (empty($page_id)) {
            wp_send_json_error('Page ID is required');
        }
        
        $page_generator = rife_pg()->page_generator;
        $result = $page_generator->delete_page($page_id);
        
        if ($result['success']) {
            wp_send_json_success($result['message']);
        } else {
            wp_send_json_error($result['message']);
        }
    }
    
    /**
     * Duplicate page
     */
    public function duplicate_page() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'rife_pg_nonce')) {
            wp_die('Security check failed');
        }
        
        // Check permissions
        if (!current_user_can('edit_pages')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        $page_id = intval($_POST['page_id']);
        
        if (empty($page_id)) {
            wp_send_json_error('Page ID is required');
        }
        
        try {
            // Get original page data
            global $wpdb;
            $table_name = $wpdb->prefix . 'rife_pg_pages';
            
            $page_data = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM $table_name WHERE page_id = %d",
                $page_id
            ));
            
            if (!$page_data) {
                wp_send_json_error('Page data not found');
            }
            
            $original_page = get_post($page_id);
            if (!$original_page) {
                wp_send_json_error('Original page not found');
            }
            
            // Create duplicate
            $content_data = json_decode($page_data->content_data, true);
            $style_data = json_decode($page_data->style_data, true);
            
            // Modify title to indicate duplicate
            if (isset($content_data['hero']['title'])) {
                $content_data['hero']['title'] = $content_data['hero']['title'] . ' (Copy)';
            }
            
            $page_generator = rife_pg()->page_generator;
            $result = $page_generator->generate_page(
                $page_data->template_id,
                $content_data,
                $style_data
            );
            
            if ($result['success']) {
                wp_send_json_success($result);
            } else {
                wp_send_json_error($result['message']);
            }
            
        } catch (Exception $e) {
            error_log('Rife PG: Page duplication error - ' . $e->getMessage());
            wp_send_json_error('An error occurred while duplicating the page');
        }
    }
    
    /**
     * Reset settings
     */
    public function reset_settings() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'rife_pg_nonce')) {
            wp_die('Security check failed');
        }
        
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        // Delete all plugin options
        delete_option('rife_pg_options');
        
        wp_send_json_success('Settings reset successfully');
    }
    
    /**
     * Clear cache
     */
    public function clear_cache() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'rife_pg_nonce')) {
            wp_die('Security check failed');
        }
        
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        // Clear any cached data
        delete_transient('rife_pg_templates_cache');
        delete_transient('rife_pg_styles_cache');
        
        // Clear WordPress object cache if available
        if (function_exists('wp_cache_flush')) {
            wp_cache_flush();
        }
        
        wp_send_json_success('Cache cleared successfully');
    }
    
    /**
     * Save styles
     */
    public function save_styles() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'rife_pg_nonce')) {
            wp_die('Security check failed');
        }
        
        // Check permissions
        if (!current_user_can('edit_pages')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        $styles = $this->sanitize_style_data($_POST['styles']);
        $style_name = sanitize_text_field($_POST['style_name']);
        
        if (empty($styles)) {
            wp_send_json_error('Style data is required');
        }
        
        if (empty($style_name)) {
            wp_send_json_error('Style name is required');
        }
        
        // Save to user meta or options
        $saved_styles = get_option('rife_pg_saved_styles', array());
        $saved_styles[$style_name] = $styles;
        
        update_option('rife_pg_saved_styles', $saved_styles);
        
        wp_send_json_success('Styles saved successfully');
    }
    
    /**
     * Load styles
     */
    public function load_styles() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'rife_pg_nonce')) {
            wp_die('Security check failed');
        }
        
        // Check permissions
        if (!current_user_can('edit_pages')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        $style_name = sanitize_text_field($_POST['style_name']);
        
        if (empty($style_name)) {
            wp_send_json_error('Style name is required');
        }
        
        $saved_styles = get_option('rife_pg_saved_styles', array());
        
        if (!isset($saved_styles[$style_name])) {
            wp_send_json_error('Style not found');
        }
        
        wp_send_json_success($saved_styles[$style_name]);
    }
    
    /**
     * Get template data
     */
    public function get_template_data() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'rife_pg_nonce')) {
            wp_die('Security check failed');
        }
        
        // Check permissions
        if (!current_user_can('edit_pages')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        $template_id = sanitize_text_field($_POST['template_id']);
        
        if (empty($template_id)) {
            wp_send_json_error('Template ID is required');
        }
        
        $template_manager = rife_pg()->template_manager;
        $template = $template_manager->get_template($template_id);
        
        if (!$template) {
            wp_send_json_error('Template not found');
        }
        
        wp_send_json_success($template);
    }
    
    /**
     * Validate template
     */
    public function validate_template() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'rife_pg_nonce')) {
            wp_die('Security check failed');
        }
        
        // Check permissions
        if (!current_user_can('edit_pages')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        $template_id = sanitize_text_field($_POST['template_id']);
        $content_data = $this->sanitize_content_data($_POST['content_data']);
        
        if (empty($template_id)) {
            wp_send_json_error('Template ID is required');
        }
        
        $template_manager = rife_pg()->template_manager;
        $validation = $template_manager->validate_template_data($template_id, $content_data);
        
        if ($validation === true) {
            wp_send_json_success('Template data is valid');
        } else {
            wp_send_json_error(array(
                'message' => 'Template validation failed',
                'errors' => $validation
            ));
        }
    }
    
    /**
     * Sanitize content data
     */
    private function sanitize_content_data($data) {
        if (!is_array($data)) {
            error_log('Rife PG: Content data is not an array: ' . print_r($data, true));
            return array();
        }

        $sanitized = array();

        foreach ($data as $section => $section_data) {
            $section = sanitize_key($section);

            if (is_array($section_data)) {
                $sanitized[$section] = array();

                foreach ($section_data as $key => $value) {
                    $key = sanitize_key($key);

                    if (is_array($value)) {
                        $sanitized[$section][$key] = $this->sanitize_array_recursive($value);
                    } else {
                        $sanitized[$section][$key] = sanitize_textarea_field($value);
                    }
                }
            } else {
                // Handle non-array section data
                $sanitized[$section] = sanitize_textarea_field($section_data);
            }
        }

        error_log('Rife PG: Sanitized content data: ' . print_r($sanitized, true));
        return $sanitized;
    }
    
    /**
     * Sanitize style data
     */
    private function sanitize_style_data($data) {
        if (!is_array($data)) {
            return array();
        }
        
        $sanitized = array();
        
        foreach ($data as $key => $value) {
            $key = sanitize_key($key);
            
            // Sanitize based on field type
            if (strpos($key, 'color') !== false) {
                $sanitized[$key] = sanitize_hex_color($value);
            } elseif (in_array($key, array('font_size_base', 'line_height', 'section_padding', 'container_width', 'border_radius'))) {
                $sanitized[$key] = floatval($value);
            } else {
                $sanitized[$key] = sanitize_text_field($value);
            }
        }
        
        return $sanitized;
    }
    
    /**
     * Sanitize array recursively
     */
    private function sanitize_array_recursive($array) {
        $sanitized = array();
        
        foreach ($array as $key => $value) {
            $key = sanitize_key($key);
            
            if (is_array($value)) {
                $sanitized[$key] = $this->sanitize_array_recursive($value);
            } else {
                $sanitized[$key] = sanitize_textarea_field($value);
            }
        }
        
        return $sanitized;
    }

    /**
     * Get dummy content for template
     */
    public function get_dummy_content() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'rife_pg_generate')) {
            wp_die('Security check failed');
        }

        // Check permissions
        if (!current_user_can('edit_pages')) {
            wp_send_json_error('Insufficient permissions');
        }

        $template_id = sanitize_text_field($_POST['template_id']);

        if (empty($template_id)) {
            wp_send_json_error('Template ID is required');
        }

        // Load dummy content from template defaults
        $defaults_file = RIFE_PG_PLUGIN_DIR . 'templates/' . $template_id . '/defaults.php';

        if (!file_exists($defaults_file)) {
            wp_send_json_error('Template defaults not found');
        }

        $dummy_content = include $defaults_file;

        if (!is_array($dummy_content)) {
            wp_send_json_error('Invalid dummy content format');
        }

        wp_send_json_success(array(
            'dummy_content' => $dummy_content,
            'template_id' => $template_id
        ));
    }

    /**
     * Handle admin-post form submission for page generation
     */
    public function handle_admin_post_generate_page() {
        error_log('Rife PG: handle_admin_post_generate_page called');
        error_log('Rife PG: POST data: ' . print_r($_POST, true));

        // Verify nonce
        if (!wp_verify_nonce($_POST['rife_pg_nonce'], 'rife_pg_generate')) {
            error_log('Rife PG: Nonce verification failed');
            wp_die('Security check failed');
        }

        error_log('Rife PG: Nonce verification passed');

        // Check permissions
        if (!current_user_can('edit_pages')) {
            wp_die('Insufficient permissions');
        }

        try {
            // Get form data
            $template_id = sanitize_text_field($_POST['template_id']);
            $auto_publish = isset($_POST['auto_publish']) ? (bool) $_POST['auto_publish'] : false;

            error_log('Rife PG: Template ID: ' . $template_id);
            error_log('Rife PG: Auto publish: ' . ($auto_publish ? 'true' : 'false'));

            // Validate template ID
            if (empty($template_id)) {
                wp_die('Template ID is required');
            }

            // Collect form data from POST
            $form_data = array();
            foreach ($_POST as $key => $value) {
                if (!in_array($key, array('action', 'rife_pg_nonce', 'template_id', 'auto_publish'))) {
                    $form_data[$key] = sanitize_textarea_field($value);
                }
            }

            error_log('Rife PG: Form data collected: ' . count($form_data) . ' fields');

            // Generate page using simple string replacement
            $page_generator = rife_pg()->page_generator;
            $result = $page_generator->generate_page_simple($template_id, $form_data, $auto_publish);

            if ($result['success']) {
                // Redirect to generated pages with success message
                $redirect_url = add_query_arg(array(
                    'page' => 'rife-pg-pages',
                    'generated' => 'success',
                    'page_id' => $result['page_id']
                ), admin_url('admin.php'));

                wp_redirect($redirect_url);
                exit;
            } else {
                wp_die('Error generating page: ' . $result['message']);
            }

        } catch (Exception $e) {
            error_log('Rife PG: Page generation error - ' . $e->getMessage());
            wp_die('An error occurred while generating the page: ' . $e->getMessage());
        }
    }

    /**
     * Convert structured form data from JavaScript to flat format expected by page generator
     */
    private function convert_structured_form_data($raw_form_data) {
        $form_data = array();

        // Extract content data
        $content = $raw_form_data['content'] ?? array();

        // Use a more comprehensive approach - extract all fields from all sections
        foreach ($content as $section_name => $section_data) {
            if (is_array($section_data)) {
                foreach ($section_data as $field_name => $field_value) {
                    // Create the flattened field name
                    $flat_field_name = $section_name . '_' . $field_name;

                    // Sanitize based on field type/name
                    if (strpos($field_name, 'email') !== false) {
                        $form_data[$flat_field_name] = sanitize_email($field_value);
                    } elseif (strpos($field_name, 'url') !== false || strpos($field_name, 'link') !== false) {
                        $form_data[$flat_field_name] = esc_url_raw($field_value);
                    } elseif (strpos($field_name, 'description') !== false || strpos($field_name, 'subtitle') !== false || strpos($field_name, 'text') !== false) {
                        $form_data[$flat_field_name] = sanitize_textarea_field($field_value);
                    } else {
                        $form_data[$flat_field_name] = sanitize_text_field($field_value);
                    }
                }
            }
        }

        // Also extract with original field names for backward compatibility
        // Hero section
        if (isset($content['hero'])) {
            $form_data['hero_title'] = sanitize_text_field($content['hero']['title'] ?? '');
            $form_data['hero_subtitle'] = sanitize_textarea_field($content['hero']['subtitle'] ?? '');
            $form_data['hero_cta_text'] = sanitize_text_field($content['hero']['cta_text'] ?? '');
            $form_data['hero_cta_link'] = esc_url_raw($content['hero']['cta_link'] ?? '');
        }

        // Benefits section - extract individual benefits and also section titles
        if (isset($content['benefits'])) {
            // Extract section titles if they exist
            $form_data['benefits_title'] = sanitize_text_field($content['benefits']['benefits_title'] ?? 'Our Key Benefits');
            $form_data['benefits_subtitle'] = sanitize_textarea_field($content['benefits']['benefits_subtitle'] ?? 'Why choose our solution');

            // Extract individual benefits
            for ($i = 1; $i <= 6; $i++) {
                $form_data["benefit_{$i}_title"] = sanitize_text_field($content['benefits']["benefit_{$i}_title"] ?? '');
                $form_data["benefit_{$i}_description"] = sanitize_textarea_field($content['benefits']["benefit_{$i}_desc"] ?? $content['benefits']["benefit_{$i}_description"] ?? '');
            }
        }

        // Services section
        if (isset($content['services'])) {
            $form_data['services_title'] = sanitize_text_field($content['services']['services_title'] ?? 'Our Services');
            $form_data['services_subtitle'] = sanitize_textarea_field($content['services']['services_subtitle'] ?? 'What we offer');

            // Extract service plans
            for ($i = 1; $i <= 3; $i++) {
                $form_data["plan_{$i}_title"] = sanitize_text_field($content['services']["plan_{$i}_title"] ?? '');
                $form_data["plan_{$i}_price"] = sanitize_text_field($content['services']["plan_{$i}_price"] ?? '');
                $form_data["plan_{$i}_features"] = sanitize_textarea_field($content['services']["plan_{$i}_features"] ?? '');
            }
        }

        // Testimonials section
        if (isset($content['testimonials'])) {
            $form_data['testimonials_title'] = sanitize_text_field($content['testimonials']['testimonials_title'] ?? 'What Our Clients Say');
            $form_data['testimonials_subtitle'] = sanitize_textarea_field($content['testimonials']['testimonials_subtitle'] ?? 'Real feedback from real customers');

            for ($i = 1; $i <= 6; $i++) {
                $form_data["testimonial_{$i}_name"] = sanitize_text_field($content['testimonials']["testimonial_{$i}_name"] ?? '');
                $form_data["testimonial_{$i}_text"] = sanitize_textarea_field($content['testimonials']["testimonial_{$i}_text"] ?? '');
            }
        }

        // Process section
        if (isset($content['process'])) {
            $form_data['process_title'] = sanitize_text_field($content['process']['process_title'] ?? $content['process']['process_section_title'] ?? 'Our Process');
            $form_data['process_subtitle'] = sanitize_textarea_field($content['process']['process_subtitle'] ?? $content['process']['process_section_subtitle'] ?? 'How we work');

            for ($i = 1; $i <= 6; $i++) {
                $form_data["process_{$i}_title"] = sanitize_text_field($content['process']["process_{$i}_title"] ?? $content['process']["step_{$i}_title"] ?? '');
                $form_data["process_{$i}_description"] = sanitize_textarea_field($content['process']["process_{$i}_description"] ?? $content['process']["step_{$i}_description"] ?? '');
                // Also create step_ versions for backward compatibility
                $form_data["step_{$i}_title"] = $form_data["process_{$i}_title"];
                $form_data["step_{$i}_description"] = $form_data["process_{$i}_description"];
            }
        }

        // About section
        if (isset($content['about'])) {
            $form_data['about_title'] = sanitize_text_field($content['about']['about_title'] ?? '');
            $form_data['about_subtitle'] = sanitize_textarea_field($content['about']['about_subtitle'] ?? '');
            $form_data['about_description'] = sanitize_textarea_field($content['about']['about_description'] ?? '');

            // Extract stats with multiple possible field name formats
            for ($i = 1; $i <= 4; $i++) {
                $form_data["stat_{$i}_number"] = sanitize_text_field(
                    $content['about']["stat_{$i}_number"] ??
                    $content['about']["about_stats_{$i}_number"] ?? ''
                );
                $form_data["stat_{$i}_label"] = sanitize_text_field(
                    $content['about']["stat_{$i}_label"] ??
                    $content['about']["about_stats_{$i}_label"] ?? ''
                );
            }
        }

        // Contact section
        if (isset($content['contact'])) {
            $form_data['contact_email'] = sanitize_email($content['contact']['contact_email'] ?? '');
            $form_data['contact_phone'] = sanitize_text_field($content['contact']['contact_phone'] ?? '');
            $form_data['contact_address'] = sanitize_textarea_field($content['contact']['contact_address'] ?? '');
        }

        // Final CTA section
        if (isset($content['final_cta'])) {
            $form_data['final_cta_title'] = sanitize_text_field($content['final_cta']['final_cta_title'] ?? '');
            $form_data['final_cta_subtitle'] = sanitize_textarea_field($content['final_cta']['final_cta_subtitle'] ?? '');
            $form_data['final_cta_button_text'] = sanitize_text_field($content['final_cta']['final_cta_button_text'] ?? '');
            $form_data['final_cta_button_link'] = esc_url_raw($content['final_cta']['final_cta_button_link'] ?? '');
        }

        // Style data
        $styles = $raw_form_data['styles'] ?? array();
        $style_keys = array(
            'primary_color', 'secondary_color', 'accent_color', 'text_color', 'background_color',
            'heading_font', 'body_font', 'font_size_base', 'line_height', 
            'section_padding', 'container_width', 'border_radius', 'button_style', 'shadow_style'
        );

        foreach ($style_keys as $key) {
            if (isset($styles[$key])) {
                $form_data[$key] = sanitize_text_field($styles[$key]);
            }
        }

        return $form_data;
    }
}