<?php
/**
 * Test for Bulk Generate Error Display
 * 
 * This test verifies that error messages during bulk generation are displayed in the header.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Rife_PG_Bulk_Generate_Error_Display_Test {
    
    public function run() {
        echo "<h2>Testing Bulk Generate Error Display in Header</h2>";
        
        // Test 1: Check if the new status message element exists in header
        echo "<h3>Test 1: Checking for status message element in header</h3>";
        $header_element = $this->check_header_element();
        if ($header_element) {
            echo "<p style='color: green;'>✓ Status message element found in header</p>";
        } else {
            echo "<p style='color: red;'>✗ Status message element not found in header</p>";
        }
        
        // Test 1.1: Check if the new status message element has proper styling
        echo "<h3>Test 1.1: Checking styling of status message element in header</h3>";
        $header_styling = $this->check_header_element_styling();
        if ($header_styling) {
            echo "<p style='color: green;'>✓ Status message element has proper styling</p>";
        } else {
            echo "<p style='color: red;'>✗ Status message element does not have proper styling</p>";
        }
        
        // Test 1.2: Check if the new status message element has CSS styles
        echo "<h3>Test 1.2: Checking CSS styles of status message element in header</h3>";
        $header_css = $this->check_header_element_css();
        if ($header_css) {
            echo "<p style='color: green;'>✓ Status message element has CSS styles</p>";
        } else {
            echo "<p style='color: red;'>✗ Status message element does not have CSS styles</p>";
        }
        
        // Test 2: Check if the old status message element is hidden
        echo "<h3>Test 2: Checking if old status message element is hidden</h3>";
        $old_element_hidden = $this->check_old_element_hidden();
        if ($old_element_hidden) {
            echo "<p style='color: green;'>✓ Old status message element is hidden</p>";
        } else {
            echo "<p style='color: red;'>✗ Old status message element is not hidden</p>";
        }
        
        // Test 3: Check if JavaScript references the new element
        echo "<h3>Test 3: Checking if JavaScript references the new element</h3>";
        $js_updated = $this->check_javascript_reference();
        if ($js_updated) {
            echo "<p style='color: green;'>✓ JavaScript references the new status message element</p>";
        } else {
            echo "<p style='color: red;'>✗ JavaScript does not reference the new status message element</p>";
        }
        
        // Overall result
        echo "<h3>Overall Result</h3>";
        if ($header_element && $header_styling && $header_css && $old_element_hidden && $js_updated) {
            echo "<p style='color: green; font-weight: bold;'>✓ All tests passed. Bulk generate error messages should now be displayed in the header.</p>";
        } else {
            echo "<p style='color: red; font-weight: bold;'>✗ Some tests failed. Please review the implementation.</p>";
        }
    }
    
    private function check_header_element() {
        // Check if the page-generator.php file contains the new element in header
        $file_path = RIFE_PG_PLUGIN_DIR . 'admin/views/page-generator.php';
        if (file_exists($file_path)) {
            $content = file_get_contents($file_path);
            return strpos($content, 'bulk-status-message-header') !== false;
        }
        return false;
    }
    
    private function check_header_element_styling() {
        // Check if the page-generator.php file contains the new element with proper styling
        $file_path = RIFE_PG_PLUGIN_DIR . 'admin/views/page-generator.php';
        if (file_exists($file_path)) {
            $content = file_get_contents($file_path);
            return strpos($content, 'word-wrap: break-word; overflow-wrap: break-word; max-width: 100%;') !== false;
        }
        return false;
    }
    
    private function check_header_element_css() {
        // Check if the page-generator.php file contains the CSS styles for notice elements
        $file_path = RIFE_PG_PLUGIN_DIR . 'admin/views/page-generator.php';
        if (file_exists($file_path)) {
            $content = file_get_contents($file_path);
            return strpos($content, '#bulk-status-message-header .notice') !== false;
        }
        return false;
    }
    
    private function check_old_element_hidden() {
        // Check if the old element has display: none
        $file_path = RIFE_PG_PLUGIN_DIR . 'admin/views/page-generator.php';
        if (file_exists($file_path)) {
            $content = file_get_contents($file_path);
            return strpos($content, 'display: none;') !== false;
        }
        return false;
    }
    
    private function check_javascript_reference() {
        // Check if admin.js references the new element ID
        $file_path = RIFE_PG_PLUGIN_DIR . 'admin/js/admin.js';
        if (file_exists($file_path)) {
            $content = file_get_contents($file_path);
            return strpos($content, 'bulk-status-message-header') !== false;
        }
        return false;
    }
}

// Run the test
$test = new Rife_PG_Bulk_Generate_Error_Display_Test();
$test->run();
?>