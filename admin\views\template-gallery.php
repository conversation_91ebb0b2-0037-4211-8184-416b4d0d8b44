<?php
/**
 * Template Gallery Admin View
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap rife-pg-admin">
    <h1 class="wp-heading-inline">
        <?php _e('Template Gallery', 'rife-pagegenerator'); ?>
        <span class="title-count"><?php echo count($templates); ?></span>
    </h1>
    
    <p class="description">
        <?php _e('Choose from our collection of professionally designed landing page templates. Click on any template to preview it or start creating your page.', 'rife-pagegenerator'); ?>
    </p>
    
    <div class="rife-pg-gallery-header">
        <div class="gallery-filters">
            <button class="filter-btn active" data-category="all">
                <?php _e('All Templates', 'rife-pagegenerator'); ?>
            </button>
            <?php foreach ($categories as $category_id => $category_name): ?>
                <button class="filter-btn" data-category="<?php echo esc_attr($category_id); ?>">
                    <?php echo esc_html($category_name); ?>
                </button>
            <?php endforeach; ?>
        </div>
        
        <div class="gallery-search">
            <input type="search" id="template-search" placeholder="<?php _e('Search templates...', 'rife-pagegenerator'); ?>">
            <span class="dashicons dashicons-search"></span>
        </div>
    </div>
    
    <div class="rife-pg-template-gallery">
        <?php if (empty($templates)): ?>
            <div class="no-templates">
                <div class="no-templates-icon">
                    <span class="dashicons dashicons-layout"></span>
                </div>
                <h3><?php _e('No Templates Available', 'rife-pagegenerator'); ?></h3>
                <p><?php _e('There are no templates available at the moment. Please check back later.', 'rife-pagegenerator'); ?></p>
            </div>
        <?php else: ?>
            <div class="template-grid">
                <?php foreach ($templates as $template): ?>
                    <div class="template-card" data-category="<?php echo esc_attr($template['category']); ?>" data-template-id="<?php echo esc_attr($template['id']); ?>">
                        <div class="template-thumbnail">
                            <img src="<?php echo esc_url($template['thumbnail']); ?>" 
                                 alt="<?php echo esc_attr($template['name']); ?>"
                                 loading="lazy">
                            <div class="template-overlay">
                                <div class="template-actions">
                                    <button class="btn btn-primary preview-template"
                                            data-template-id="<?php echo esc_attr($template['id']); ?>"
                                            title="<?php _e('Preview Template', 'rife-pagegenerator'); ?>">
                                        <span class="dashicons dashicons-visibility"></span>
                                        <?php _e('Preview', 'rife-pagegenerator'); ?>
                                    </button>
                                    <?php if ($template['id'] === 'seo-comprehensive'): ?>
                                    <button type="button" class="btn btn-secondary generate-prompt-button"
                                            data-template-id="<?php echo esc_attr($template['id']); ?>"
                                            title="<?php _e('Generate AI Prompt', 'rife-pagegenerator'); ?>">
                                        <span class="dashicons dashicons-format-aside"></span>
                                        <?php _e('Prompt', 'rife-pagegenerator'); ?>
                                    </button>
                                    <?php endif; ?>
                                    <a href="<?php echo esc_url(admin_url('admin.php?page=rife-pg-generator&template=' . $template['id'])); ?>"
                                       class="btn btn-secondary use-template"
                                       title="<?php _e('Use This Template', 'rife-pagegenerator'); ?>">
                                        <span class="dashicons dashicons-plus-alt"></span>
                                        <?php _e('Use Template', 'rife-pagegenerator'); ?>
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <div class="template-info">
                            <div class="template-header">
                                <h3 class="template-title"><?php echo esc_html($template['name']); ?></h3>
                                <span class="template-category"><?php echo esc_html($categories[$template['category']]); ?></span>
                            </div>
                            <p class="template-description"><?php echo esc_html($template['description']); ?></p>
                            
                            <div class="template-sections">
                                <span class="sections-label"><?php _e('Sections:', 'rife-pagegenerator'); ?></span>
                                <div class="sections-list">
                                    <?php foreach ($template['sections'] as $section): ?>
                                        <span class="section-tag"><?php echo esc_html(ucfirst(str_replace('-', ' ', $section))); ?></span>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- AI Prompt Generator Modal -->
<div id="ai-prompt-modal" class="rife-pg-modal" style="display: none;">
    <div class="modal-overlay"></div>
    <div class="modal-content" style="max-width: 800px; height: auto;">
        <div class="modal-header">
            <h2 style="display: flex; align-items: center; gap: 10px;"><span class="dashicons dashicons-robot" style="font-size: 24px;"></span> <?php _e('AI Content Prompt Generator', 'rife-pagegenerator'); ?></h2>
            <button class="modal-close" type="button"><span class="dashicons dashicons-no-alt"></span></button>
        </div>
        <div class="modal-body" style="overflow-y: auto; padding: 0 20px 20px 20px;">
            <p class="description" style="margin-top: 10px;"><?php _e('Generate content for multiple pages at once. Enter up to 5 keywords (one per line), and we will create a custom prompt for your AI tool. The generated prompt now includes Yoast SEO fields for automatic SEO optimization.', 'rife-pagegenerator'); ?></p>
            
            <div style="display: grid; grid-template-columns: 1fr 2fr; gap: 20px;">
                <div class="prompt-inputs">
                    <div class="field-group">
                        <label for="prompt-keywords"><strong><?php _e('Enter 1-5 Keywords', 'rife-pagegenerator'); ?></strong></label>
                        <textarea id="prompt-keywords" rows="5" placeholder="<?php _e('Jasa SEO Jakarta\nDigital Marketing Agency\nWeb Design Murah\n...', 'rife-pagegenerator'); ?>"></textarea>
                        <p class="description" style="font-size: 12px; margin-top: 5px;"><?php _e('One keyword per line.', 'rife-pagegenerator'); ?></p>
                    </div>
                </div>

                <div class="prompt-output-wrapper">
                    <div class="field-group">
                        <label for="ai-prompt-output"><strong><?php _e('Your Generated Prompt', 'rife-pagegenerator'); ?></strong></label>
                        <textarea id="ai-prompt-output" readonly rows="10" style="background: #f8fafc; white-space: pre-wrap;"></textarea>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer" style="padding: 15px 20px; border-top: 1px solid #e2e8f0; text-align: right; background: #f8fafc; border-bottom-left-radius: 12px; border-bottom-right-radius: 12px;">
             <button type="button" class="button button-primary copy-prompt-button">
                <span class="dashicons dashicons-clipboard"></span> <?php _e('Copy Prompt to Clipboard', 'rife-pagegenerator'); ?>
             </button>
             <span id="copy-prompt-feedback" style="display: none; color: #10b981; margin-left: 10px; vertical-align: middle;"><?php _e('Copied!', 'rife-pagegenerator'); ?></span>
        </div>
    </div>
</div>


<!-- Template Preview Modal -->
<div id="template-preview-modal" class="rife-pg-modal" style="display: none;">
    <div class="modal-overlay"></div>
    <div class="modal-content">
        <div class="modal-header">
            <h2><?php _e('Template Preview', 'rife-pagegenerator'); ?></h2>
            <button class="modal-close" type="button">
                <span class="dashicons dashicons-no-alt"></span>
            </button>
        </div>
        <div class="modal-body">
            <div class="preview-loading">
                <span class="spinner is-active"></span>
                <p><?php _e('Loading preview...', 'rife-pagegenerator'); ?></p>
            </div>
            <iframe id="template-preview-frame" src="" frameborder="0"></iframe>
        </div>
        <div class="modal-footer">
            <button type="button" class="button button-secondary modal-close">
                <?php _e('Close', 'rife-pagegenerator'); ?>
            </button>
            <a href="#" class="button button-primary use-template-from-preview">
                <?php _e('Use This Template', 'rife-pagegenerator'); ?>
            </a>
        </div>
    </div>
</div>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Template filtering
    $('.filter-btn').on('click', function() {
        var category = $(this).data('category');
        
        $('.filter-btn').removeClass('active');
        $(this).addClass('active');
        
        if (category === 'all') {
            $('.template-card').show();
        } else {
            $('.template-card').hide();
            $('.template-card[data-category="' + category + '"]').show();
        }
    });
    
    // Template search
    $('#template-search').on('input', function() {
        var searchTerm = $(this).val().toLowerCase();
        
        $('.template-card').each(function() {
            var templateName = $(this).find('.template-title').text().toLowerCase();
            var templateDesc = $(this).find('.template-description').text().toLowerCase();
            
            if (templateName.includes(searchTerm) || templateDesc.includes(searchTerm)) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });
    
    // Template preview in modal
    $('.preview-template').on('click', function() {
        var templateId = $(this).data('template-id');
        showTemplatePreview(templateId);
    });

    // Template preview in new tab
    $('.preview-template-new-tab').on('click', function() {
        var templateId = $(this).data('template-id');
        openTemplateInNewTab(templateId);
    });
    
    // Modal controls
    $('.modal-close, .modal-overlay').on('click', function() {
        $(this).closest('.rife-pg-modal').hide();
        $('#template-preview-frame').attr('src', '');
    });
    
    function showTemplatePreview(templateId) {
        var modal = $('#template-preview-modal');
        var frame = $('#template-preview-frame');
        
        // Show modal and loading state
        modal.show();
        $('.preview-loading').show();
        frame.hide();
        
        // Make AJAX request to get preview URL
        $.ajax({
            url: rifePgAdmin.ajaxUrl,
            type: 'POST',
            data: {
                action: 'rife_pg_preview_template',
                template_id: templateId,
                nonce: rifePgAdmin.nonce
            },
            success: function(response) {
                console.log('Preview response:', response); // Debug log
                if (response.success) {
                    frame.attr('src', response.data.preview_url);
                    frame.on('load', function() {
                        $('.preview-loading').hide();
                        frame.show();
                    });

                    // Update use template button
                    $('.use-template-from-preview').attr('href',
                        '<?php echo admin_url('admin.php?page=rife-pg-generator&template='); ?>' + templateId
                    );
                } else {
                    $('.preview-loading').html('<p><?php _e('Failed to load preview: ', 'rife-pagegenerator'); ?>' + (response.data || 'Unknown error') + '</p>');
                }
            },
            error: function(xhr, status, error) {
                console.error('Preview AJAX error:', xhr, status, error); // Debug log
                $('.preview-loading').html('<p><?php _e('Failed to load preview', 'rife-pagegenerator'); ?></p>');
            }
        });
    }

    // Open template preview in new tab
    function openTemplateInNewTab(templateId) {
        // Make AJAX request to get preview URL
        $.ajax({
            url: rifePgAdmin.ajaxUrl,
            type: 'POST',
            data: {
                action: 'rife_pg_preview_template',
                template_id: templateId,
                nonce: rifePgAdmin.nonce
            },
            success: function(response) {
                console.log('New tab preview response:', response); // Debug log
                if (response.success) {
                    // Open preview URL in new tab
                    window.open(response.data.preview_url, '_blank');
                } else {
                    alert('<?php _e('Failed to load preview', 'rife-pagegenerator'); ?>');
                }
            },
            error: function(xhr, status, error) {
                console.error('New tab preview AJAX error:', xhr, status, error); // Debug log
                alert('<?php _e('Failed to load preview', 'rife-pagegenerator'); ?>');
            }
        });
    }
});
</script>