<?php
/**
 * Page Generator for Rife PageGenerator
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Rife_PG_Page_Generator {
    
    /**
     * Constructor
     */
    public function __construct() {
        // Hook into WordPress
        require_once RIFE_PG_PLUGIN_DIR . 'modules/class-image-generator.php';
    }
    
    /**
     * Generate a new WordPress page using simple string replacement
     */
    public function generate_page_simple($template_id, $form_data, $auto_publish = false) {
        try {
            error_log('Rife PG: Starting simple page generation...');
            error_log('Rife PG: Template ID: ' . $template_id);
            error_log('Rife PG: Form data keys: ' . print_r(array_keys($form_data), true));

            // Get template file path
            $template_path = RIFE_PG_PLUGIN_DIR . 'templates/' . $template_id . '/template.php';
            error_log('Rife PG: Template path: ' . $template_path);

            if (!file_exists($template_path)) {
                error_log('Rife PG: Template file not found: ' . $template_path);
                return array(
                    'success' => false,
                    'message' => 'Template file not found: ' . $template_id
                );
            }

            // Read template content
            $template_content = file_get_contents($template_path);

            if (!$template_content) {
                error_log('Rife PG: Failed to read template content from: ' . $template_path);
                return array(
                    'success' => false,
                    'message' => 'Failed to read template content'
                );
            }

            error_log('Rife PG: Template content loaded, length: ' . strlen($template_content));

            // --- FONT & STYLE FIX ---

            // 1. Generate CSS from style data
            error_log('Rife PG: Generating CSS from style data...');
            $style_customizer = new Rife_PG_Style_Customizer();
            $generated_css = $style_customizer->generate_css($form_data);
            
            // 2. Generate Google Fonts link
            $heading_font = $form_data['heading_font'] ?? 'Inter';
            $body_font = $form_data['body_font'] ?? 'Inter';
            
            error_log('Rife PG: Heading font: ' . $heading_font);
            error_log('Rife PG: Body font: ' . $body_font);
            
            $google_fonts_url = $this->get_google_fonts_url(array($heading_font, $body_font));
            $google_fonts_link_html = '';
            if ($google_fonts_url) {
                $google_fonts_link_html = '<link rel="preconnect" href="https://fonts.googleapis.com">' .
                                          '<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>' .
                                          '<link href="' . esc_url($google_fonts_url) . '" rel="stylesheet">';
                error_log('Rife PG: Google Fonts URL generated: ' . $google_fonts_url);
            } else {
                error_log('Rife PG: No Google Fonts URL generated - using system fonts only');
            }

            // 3. Replace placeholders in the template
            $template_content = str_replace('{{GENERATED_STYLES}}', $generated_css, $template_content);
            $template_content = str_replace('{{GOOGLE_FONTS_LINK}}', $google_fonts_link_html, $template_content);
            
            error_log('Rife PG: Generated CSS length: ' . strlen($generated_css));
            error_log('Rife PG: Google Fonts HTML length: ' . strlen($google_fonts_link_html));

            // --- END OF FIX ---

            // Replace content placeholders with form data
            error_log('Rife PG: Replacing placeholders with form data...');
            $html_content = $this->replace_placeholders($template_content, $form_data);

            error_log('Rife PG: Placeholders replaced, final length: ' . strlen($html_content));

            // Create WordPress page
            $page_title = $form_data['hero_title'] ?: 'Generated Landing Page';
            error_log('Rife PG: Creating page with title: ' . $page_title);
            
            $page_data = array(
                'post_title'    => $page_title,
                'post_content'  => $html_content,
                'post_status'   => $auto_publish ? 'publish' : 'draft',
                'post_type'     => 'page',
                'post_author'   => get_current_user_id(),
                'meta_input'    => array(
                    '_rife_pg_generated' => true,
                    '_rife_pg_template_id' => $template_id,
                    '_rife_pg_generated_date' => current_time('mysql')
                )
            );

            // Add Yoast SEO metadata if available
            error_log('Rife PG: Extracting Yoast SEO data...');
            $yoast_seo_data = $this->extract_yoast_seo_data($form_data);
            if (!empty($yoast_seo_data)) {
                error_log('Rife PG: Yoast SEO data extracted: ' . print_r($yoast_seo_data, true));
                $page_data['meta_input'] = array_merge($page_data['meta_input'], $yoast_seo_data);
            } else {
                error_log('Rife PG: No Yoast SEO data to add');
            }

            error_log('Rife PG: Inserting page with data: ' . print_r($page_data, true));
            $page_id = wp_insert_post($page_data);

            if (is_wp_error($page_id)) {
                error_log('Rife PG: Failed to create page: ' . $page_id->get_error_message());
                return array(
                    'success' => false,
                    'message' => 'Failed to create page: ' . $page_id->get_error_message()
                );
            }

            error_log('Rife PG: Page created successfully with ID: ' . $page_id);

            // Generate and set featured image if keyword is available
            if (!empty($form_data['yoast_focus_keyphrase'])) {
                error_log('Rife PG: Generating featured image for keyword: ' . $form_data['yoast_focus_keyphrase']);
                $this->generate_and_set_featured_image($page_id, $form_data['yoast_focus_keyphrase']);
            } else {
                error_log('Rife PG: No focus keyphrase provided, skipping image generation');
            }

            return array(
                'success' => true,
                'message' => 'Page generated successfully!',
                'page_id' => $page_id,
                'page_url' => get_permalink($page_id),
                'edit_url' => admin_url('post.php?post=' . $page_id . '&action=edit')
            );

        } catch (Exception $e) {
            error_log('Rife PG: Simple page generation error - ' . $e->getMessage());
            error_log('Rife PG: Exception trace: ' . $e->getTraceAsString());
            return array(
                'success' => false,
                'message' => 'An error occurred while generating the page: ' . $e->getMessage()
            );
        }
    }

    /**
     * Generate Google Fonts URL
     */
    private function get_google_fonts_url($fonts) {
        try {
            error_log('Rife PG: Generating Google Fonts URL for fonts: ' . print_r($fonts, true));
            
            $font_families = array();
            
            // Get unique fonts
            $unique_fonts = array_unique($fonts);
            error_log('Rife PG: Unique fonts: ' . print_r($unique_fonts, true));
            
            foreach ($unique_fonts as $font) {
                // Skip empty fonts
                if (empty($font)) {
                    error_log('Rife PG: Skipping empty font');
                    continue;
                }
                
                // Basic fonts that don't need to be loaded from Google
                if (in_array($font, array('Arial', 'Helvetica', 'Georgia', 'Times New Roman'))) {
                    error_log('Rife PG: Skipping basic font: ' . $font);
                    continue;
                }
                
                // Add font with weight variations - fix URL encoding
                $encoded_font = str_replace(' ', '+', $font) . ':wght@400;700';
                $font_families[] = $encoded_font;
                error_log('Rife PG: Added font family: ' . $encoded_font);
            }
            
            if (empty($font_families)) {
                error_log('Rife PG: No font families to generate URL for');
                return false;
            }
            
            // Build proper Google Fonts URL
            $url = 'https://fonts.googleapis.com/css2?';
            $url .= 'family=' . implode('&family=', $font_families);
            $url .= '&display=swap';
            
            error_log('Rife PG: Generated Google Fonts URL: ' . $url);
            return $url;
            
        } catch (Exception $e) {
            error_log('Rife PG: Error generating Google Fonts URL - ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Replace placeholders in template with form data
     */
    private function replace_placeholders($template_content, $form_data) {
        // Load template defaults to get all possible placeholders
        $template_id = $form_data['template_id'] ?? 'seo-comprehensive';
        $defaults_file = RIFE_PG_PLUGIN_DIR . 'templates/' . $template_id . '/defaults.php';

        $defaults = array();
        if (file_exists($defaults_file)) {
            $defaults = include $defaults_file;
        }

        // Merge form data with defaults (form data takes precedence)
        $all_data = array_merge($defaults, $form_data);

        $replacements = array();

        // Convert all data to uppercase placeholder format and add to replacements
        foreach ($all_data as $key => $value) {
            $placeholder = '{{' . strtoupper($key) . '}}';

            // Handle different data types appropriately
            if (is_string($value)) {
                if (filter_var($value, FILTER_VALIDATE_URL) || strpos($key, '_link') !== false) {
                    $replacements[$placeholder] = esc_url($value);
                } elseif (strpos($key, '_color') !== false) {
                    $replacements[$placeholder] = esc_attr($value);
                } else {
                    $replacements[$placeholder] = esc_html($value);
                }
            } elseif (is_numeric($value)) {
                $replacements[$placeholder] = intval($value);
            } else {
                $replacements[$placeholder] = esc_html(strval($value));
            }
        }

        // Add comprehensive specific mappings for template placeholders
        $specific_mappings = array(
            // Hero section
            '{{HERO_TITLE}}' => esc_html($form_data['hero_title'] ?? $defaults['hero_title'] ?? 'Your Business Success Starts Here'),
            '{{HERO_SUBTITLE}}' => esc_html($form_data['hero_subtitle'] ?? $defaults['hero_subtitle'] ?? 'Transform your business with our professional solutions.'),
            '{{HERO_CTA_TEXT}}' => esc_html($form_data['hero_cta_text'] ?? $defaults['hero_cta_text'] ?? 'Get Started'),
            '{{HERO_CTA_LINK}}' => esc_url($form_data['hero_cta_link'] ?? $defaults['hero_cta_link'] ?? 'https://example.com/contact'),

            // Section titles and subtitles
            '{{BENEFITS_TITLE}}' => esc_html($form_data['benefits_title'] ?? 'Our Key Benefits'),
            '{{BENEFITS_SUBTITLE}}' => esc_html($form_data['benefits_subtitle'] ?? 'Why choose our solution'),
            '{{SERVICES_TITLE}}' => esc_html($form_data['services_title'] ?? 'Our Services'),
            '{{SERVICES_SUBTITLE}}' => esc_html($form_data['services_subtitle'] ?? 'What we offer'),
            '{{PROCESS_TITLE}}' => esc_html($form_data['process_title'] ?? 'Our Process'),
            '{{PROCESS_SUBTITLE}}' => esc_html($form_data['process_subtitle'] ?? 'How we work'),
            '{{PRICING_TITLE}}' => esc_html($form_data['pricing_title'] ?? 'Pricing Plans'),
            '{{PRICING_SUBTITLE}}' => esc_html($form_data['pricing_subtitle'] ?? 'Choose the right plan for you'),
            '{{TESTIMONIALS_TITLE}}' => esc_html($form_data['testimonials_title'] ?? 'What Our Clients Say'),
            '{{TESTIMONIALS_SUBTITLE}}' => esc_html($form_data['testimonials_subtitle'] ?? 'Real feedback from real customers'),
            '{{ABOUT_TITLE}}' => esc_html($form_data['about_title'] ?? 'About Us'),
            '{{ABOUT_SUBTITLE}}' => esc_html($form_data['about_subtitle'] ?? 'Our story and mission'),
            '{{CONTACT_TITLE}}' => esc_html($form_data['contact_title'] ?? 'Get In Touch'),
            '{{CONTACT_SUBTITLE}}' => esc_html($form_data['contact_subtitle'] ?? 'Ready to get started?'),

            // Contact information
            '{{CONTACT_EMAIL}}' => esc_html($form_data['contact_email'] ?? '<EMAIL>'),
            '{{CONTACT_PHONE}}' => esc_html($form_data['contact_phone'] ?? '+****************'),
            '{{CONTACT_ADDRESS}}' => esc_html($form_data['contact_address'] ?? '123 Business St, City, State 12345'),

            // Stats
            '{{STAT_1_NUMBER}}' => esc_html($form_data['stat_1_number'] ?? '500+'),
            '{{STAT_1_LABEL}}' => esc_html($form_data['stat_1_label'] ?? 'Projects Completed'),
            '{{STAT_2_NUMBER}}' => esc_html($form_data['stat_2_number'] ?? '10+'),
            '{{STAT_2_LABEL}}' => esc_html($form_data['stat_2_label'] ?? 'Years Experience'),
            '{{STAT_3_NUMBER}}' => esc_html($form_data['stat_3_number'] ?? '98%'),
            '{{STAT_3_LABEL}}' => esc_html($form_data['stat_3_label'] ?? 'Client Satisfaction'),
            '{{STAT_4_NUMBER}}' => esc_html($form_data['stat_4_number'] ?? '24/7'),
            '{{STAT_4_LABEL}}' => esc_html($form_data['stat_4_label'] ?? 'Support Available'),

            // Benefits
            '{{BENEFIT_1_TITLE}}' => esc_html($form_data['benefit_1_title'] ?? 'Fast Performance'),
            '{{BENEFIT_1_DESCRIPTION}}' => esc_html($form_data['benefit_1_description'] ?? 'Lightning fast loading times'),
            '{{BENEFIT_2_TITLE}}' => esc_html($form_data['benefit_2_title'] ?? 'Secure'),
            '{{BENEFIT_2_DESCRIPTION}}' => esc_html($form_data['benefit_2_description'] ?? 'Enterprise-grade security'),
            '{{BENEFIT_3_TITLE}}' => esc_html($form_data['benefit_3_title'] ?? 'Easy to Use'),
            '{{BENEFIT_3_DESCRIPTION}}' => esc_html($form_data['benefit_3_description'] ?? 'Intuitive user interface'),
            '{{BENEFIT_4_TITLE}}' => esc_html($form_data['benefit_4_title'] ?? 'Reliable'),
            '{{BENEFIT_4_DESCRIPTION}}' => esc_html($form_data['benefit_4_description'] ?? '99.9% uptime guarantee'),
            '{{BENEFIT_5_TITLE}}' => esc_html($form_data['benefit_5_title'] ?? 'Scalable'),
            '{{BENEFIT_5_DESCRIPTION}}' => esc_html($form_data['benefit_5_description'] ?? 'Grows with your business'),
            '{{BENEFIT_6_TITLE}}' => esc_html($form_data['benefit_6_title'] ?? 'Support'),
            '{{BENEFIT_6_DESCRIPTION}}' => esc_html($form_data['benefit_6_description'] ?? '24/7 customer support'),

            // Testimonials
            '{{TESTIMONIAL_1_NAME}}' => esc_html($form_data['testimonial_1_name'] ?? 'John Smith'),
            '{{TESTIMONIAL_1_TEXT}}' => esc_html($form_data['testimonial_1_text'] ?? 'Great service and excellent results!'),
            '{{TESTIMONIAL_2_NAME}}' => esc_html($form_data['testimonial_2_name'] ?? 'Jane Doe'),
            '{{TESTIMONIAL_2_TEXT}}' => esc_html($form_data['testimonial_2_text'] ?? 'Highly recommend their services.'),
            '{{TESTIMONIAL_3_NAME}}' => esc_html($form_data['testimonial_3_name'] ?? 'Mike Johnson'),
            '{{TESTIMONIAL_3_TEXT}}' => esc_html($form_data['testimonial_3_text'] ?? 'Professional and reliable team.'),
            '{{TESTIMONIAL_4_NAME}}' => esc_html($form_data['testimonial_4_name'] ?? 'Sarah Wilson'),
            '{{TESTIMONIAL_4_TEXT}}' => esc_html($form_data['testimonial_4_text'] ?? 'Exceeded our expectations.'),
            '{{TESTIMONIAL_5_NAME}}' => esc_html($form_data['testimonial_5_name'] ?? 'David Brown'),
            '{{TESTIMONIAL_5_TEXT}}' => esc_html($form_data['testimonial_5_text'] ?? 'Outstanding customer service.'),
            '{{TESTIMONIAL_6_NAME}}' => esc_html($form_data['testimonial_6_name'] ?? 'Lisa Davis'),
            '{{TESTIMONIAL_6_TEXT}}' => esc_html($form_data['testimonial_6_text'] ?? 'Best investment we made.'),

            // Process steps
            '{{PROCESS_1_TITLE}}' => esc_html($form_data['process_1_title'] ?? 'Discovery'),
            '{{PROCESS_1_DESCRIPTION}}' => esc_html($form_data['process_1_description'] ?? 'We learn about your business needs'),
            '{{PROCESS_2_TITLE}}' => esc_html($form_data['process_2_title'] ?? 'Planning'),
            '{{PROCESS_2_DESCRIPTION}}' => esc_html($form_data['process_2_description'] ?? 'We create a detailed project plan'),
            '{{PROCESS_3_TITLE}}' => esc_html($form_data['process_3_title'] ?? 'Development'),
            '{{PROCESS_3_DESCRIPTION}}' => esc_html($form_data['process_3_description'] ?? 'We build your solution'),
            '{{PROCESS_4_TITLE}}' => esc_html($form_data['process_4_title'] ?? 'Testing'),
            '{{PROCESS_4_DESCRIPTION}}' => esc_html($form_data['process_4_description'] ?? 'We ensure everything works perfectly'),
            '{{PROCESS_5_TITLE}}' => esc_html($form_data['process_5_title'] ?? 'Launch'),
            '{{PROCESS_5_DESCRIPTION}}' => esc_html($form_data['process_5_description'] ?? 'We deploy your solution'),
            '{{PROCESS_6_TITLE}}' => esc_html($form_data['process_6_title'] ?? 'Support'),
            '{{PROCESS_6_DESCRIPTION}}' => esc_html($form_data['process_6_description'] ?? 'We provide ongoing support'),

            // Default placeholders for sections not yet implemented
            '{{CASE_STUDIES_TITLE}}' => 'Case Studies',
            '{{CASE_STUDIES_SUBTITLE}}' => 'Success stories from our clients',
            '{{GUARANTEES_TITLE}}' => 'Our Guarantees',
            '{{GUARANTEES_SUBTITLE}}' => 'Your success is guaranteed',
            '{{GUIDE_TITLE}}' => 'Free Resources',
            '{{GUIDE_SUBTITLE}}' => 'Helpful guides and resources',
            '{{FAQ_TITLE}}' => 'Frequently Asked Questions',
            '{{FAQ_SUBTITLE}}' => 'Common questions and answers',
            '{{LOCATIONS_TITLE}}' => 'Service Areas',
            '{{LOCATIONS_SUBTITLE}}' => 'Where we provide our services',
            '{{FOOTER_COMPANY_NAME}}' => 'Your Company',
            '{{FOOTER_TAGLINE}}' => 'Your trusted partner for success',

            // Hero section additional placeholders
            '{{HERO_PHONE_TEXT}}' => 'Call Now',
            '{{HERO_PHONE_LINK}}' => 'tel:+1234567890',

            // Services section placeholders
            '{{SERVICE_1_TITLE}}' => 'Web Development',
            '{{SERVICE_1_DESCRIPTION}}' => 'Professional web development services',
            '{{SERVICE_1_FEATURES_TITLE}}' => 'Features',
            '{{SERVICE_1_FEATURE_1}}' => 'Responsive Design',
            '{{SERVICE_1_FEATURE_2}}' => 'SEO Optimized',
            '{{SERVICE_1_FEATURE_3}}' => 'Fast Loading',
            '{{SERVICE_1_FEATURE_4}}' => 'Mobile Friendly',
            '{{SERVICE_1_FEATURE_5}}' => 'Cross Browser Compatible',
            '{{SERVICE_1_FEATURE_6}}' => 'Clean Code',

            '{{SERVICE_2_TITLE}}' => 'Digital Marketing',
            '{{SERVICE_2_DESCRIPTION}}' => 'Comprehensive digital marketing solutions',
            '{{SERVICE_2_FEATURES_TITLE}}' => 'Features',
            '{{SERVICE_2_FEATURE_1}}' => 'Social Media Marketing',
            '{{SERVICE_2_FEATURE_2}}' => 'Content Marketing',
            '{{SERVICE_2_FEATURE_3}}' => 'Email Marketing',
            '{{SERVICE_2_FEATURE_4}}' => 'PPC Advertising',
            '{{SERVICE_2_FEATURE_5}}' => 'Analytics & Reporting',
            '{{SERVICE_2_FEATURE_6}}' => 'Brand Strategy',

            '{{SERVICE_3_TITLE}}' => 'Consulting',
            '{{SERVICE_3_DESCRIPTION}}' => 'Expert business consulting services',
            '{{SERVICE_3_FEATURES_TITLE}}' => 'Features',
            '{{SERVICE_3_FEATURE_1}}' => 'Business Strategy',
            '{{SERVICE_3_FEATURE_2}}' => 'Process Optimization',
            '{{SERVICE_3_FEATURE_3}}' => 'Technology Assessment',
            '{{SERVICE_3_FEATURE_4}}' => 'Market Analysis',
            '{{SERVICE_3_FEATURE_5}}' => 'Growth Planning',
            '{{SERVICE_3_FEATURE_6}}' => 'Risk Management',

            '{{SERVICE_4_TITLE}}' => 'Support & Maintenance',
            '{{SERVICE_4_DESCRIPTION}}' => 'Ongoing support and maintenance services',
            '{{SERVICE_4_FEATURES_TITLE}}' => 'Features',
            '{{SERVICE_4_FEATURE_1}}' => '24/7 Support',
            '{{SERVICE_4_FEATURE_2}}' => 'Regular Updates',
            '{{SERVICE_4_FEATURE_3}}' => 'Security Monitoring',
            '{{SERVICE_4_FEATURE_4}}' => 'Performance Optimization',
            '{{SERVICE_4_FEATURE_5}}' => 'Backup Services',
            '{{SERVICE_4_FEATURE_6}}' => 'Technical Documentation',

            // Pricing section placeholders
            '{{PRICING_1_NAME}}' => 'Basic Plan',
            '{{PRICING_1_PRICE}}' => '$99',
            '{{PRICING_1_PERIOD}}' => '/month',
            '{{PRICING_1_FEATURE_1}}' => 'Up to 5 pages',
            '{{PRICING_1_FEATURE_2}}' => 'Basic SEO',
            '{{PRICING_1_FEATURE_3}}' => 'Email support',
            '{{PRICING_1_FEATURE_4}}' => 'Mobile responsive',
            '{{PRICING_1_FEATURE_5}}' => 'SSL certificate',
            '{{PRICING_1_FEATURE_6}}' => 'Basic analytics',
            '{{PRICING_1_CTA_TEXT}}' => 'Get Started',
            '{{PRICING_1_CTA_LINK}}' => '#contact',

            '{{PRICING_2_NAME}}' => 'Professional Plan',
            '{{PRICING_2_BADGE}}' => 'MOST POPULAR',
            '{{PRICING_2_PRICE}}' => '$199',
            '{{PRICING_2_PERIOD}}' => '/month',
            '{{PRICING_2_FEATURE_1}}' => 'Up to 15 pages',
            '{{PRICING_2_FEATURE_2}}' => 'Advanced SEO',
            '{{PRICING_2_FEATURE_3}}' => 'Priority support',
            '{{PRICING_2_FEATURE_4}}' => 'Custom design',
            '{{PRICING_2_FEATURE_5}}' => 'E-commerce ready',
            '{{PRICING_2_FEATURE_6}}' => 'Advanced analytics',
            '{{PRICING_2_FEATURE_7}}' => 'Social media integration',
            '{{PRICING_2_FEATURE_8}}' => 'Content management',
            '{{PRICING_2_CTA_TEXT}}' => 'Choose Plan',
            '{{PRICING_2_CTA_LINK}}' => '#contact',

            '{{PRICING_3_NAME}}' => 'Enterprise Plan',
            '{{PRICING_3_PRICE}}' => '$399',
            '{{PRICING_3_PERIOD}}' => '/month',
            '{{PRICING_3_FEATURE_1}}' => 'Unlimited pages',
            '{{PRICING_3_FEATURE_2}}' => 'Enterprise SEO',
            '{{PRICING_3_FEATURE_3}}' => '24/7 phone support',
            '{{PRICING_3_FEATURE_4}}' => 'Custom development',
            '{{PRICING_3_FEATURE_5}}' => 'Advanced e-commerce',
            '{{PRICING_3_FEATURE_6}}' => 'Custom integrations',
            '{{PRICING_3_FEATURE_7}}' => 'Dedicated account manager',
            '{{PRICING_3_FEATURE_8}}' => 'Advanced security',
            '{{PRICING_3_FEATURE_9}}' => 'White-label options',
            '{{PRICING_3_CTA_TEXT}}' => 'Contact Sales',
            '{{PRICING_3_CTA_LINK}}' => '#contact',

            '{{PRICING_CONSULTATION_TEXT}}' => 'Need a custom solution?',
            '{{PRICING_CONSULTATION_CTA}}' => 'Schedule Free Consultation',
            '{{PRICING_CONSULTATION_LINK}}' => '#contact',

            // Case Studies placeholders
            '{{CASE_STUDY_1_TITLE}}' => 'E-commerce Success Story',
            '{{CASE_STUDY_1_PROBLEM_TITLE}}' => 'The Challenge',
            '{{CASE_STUDY_1_PROBLEM}}' => 'Client needed to increase online sales and improve user experience.',
            '{{CASE_STUDY_1_SOLUTION_TITLE}}' => 'Our Solution',
            '{{CASE_STUDY_1_SOLUTION}}' => 'We redesigned their website with modern UX principles and optimized conversion paths.',
            '{{CASE_STUDY_1_RESULTS_TITLE}}' => 'Results Achieved',
            '{{CASE_STUDY_1_RESULT_1}}' => '300% increase in online sales',
            '{{CASE_STUDY_1_RESULT_2}}' => '50% improvement in conversion rate',
            '{{CASE_STUDY_1_RESULT_3}}' => '40% reduction in bounce rate',
            '{{CASE_STUDY_1_RESULT_4}}' => '200% increase in mobile traffic',
            '{{CASE_STUDY_1_RESULT_5}}' => '25% improvement in page load speed',

            '{{CASE_STUDY_2_TITLE}}' => 'Lead Generation Campaign',
            '{{CASE_STUDY_2_PROBLEM_TITLE}}' => 'The Challenge',
            '{{CASE_STUDY_2_PROBLEM}}' => 'B2B client struggled with generating qualified leads through digital channels.',
            '{{CASE_STUDY_2_SOLUTION_TITLE}}' => 'Our Solution',
            '{{CASE_STUDY_2_SOLUTION}}' => 'We implemented a comprehensive digital marketing strategy with targeted content and automation.',
            '{{CASE_STUDY_2_RESULTS_TITLE}}' => 'Results Achieved',
            '{{CASE_STUDY_2_RESULT_1}}' => '500% increase in qualified leads',
            '{{CASE_STUDY_2_RESULT_2}}' => '60% reduction in cost per lead',
            '{{CASE_STUDY_2_RESULT_3}}' => '80% improvement in lead quality',
            '{{CASE_STUDY_2_RESULT_4}}' => '150% increase in email subscribers',
            '{{CASE_STUDY_2_RESULT_5}}' => '35% improvement in sales conversion',

            '{{CASE_STUDY_3_TITLE}}' => 'Brand Transformation',
            '{{CASE_STUDY_3_PROBLEM_TITLE}}' => 'The Challenge',
            '{{CASE_STUDY_3_PROBLEM}}' => 'Established company needed to modernize their brand and reach younger demographics.',
            '{{CASE_STUDY_3_SOLUTION_TITLE}}' => 'Our Solution',
            '{{CASE_STUDY_3_SOLUTION}}' => 'We created a complete brand refresh with new visual identity and digital presence.',
            '{{CASE_STUDY_3_RESULTS_TITLE}}' => 'Results Achieved',
            '{{CASE_STUDY_3_RESULT_1}}' => '400% increase in social media engagement',
            '{{CASE_STUDY_3_RESULT_2}}' => '250% growth in younger demographic reach',
            '{{CASE_STUDY_3_RESULT_3}}' => '180% increase in brand awareness',
            '{{CASE_STUDY_3_RESULT_4}}' => '120% improvement in customer satisfaction',
            '{{CASE_STUDY_3_RESULT_5}}' => '90% increase in repeat customers',

            // FAQ placeholders
            '{{FAQ_1_QUESTION}}' => 'How long does a typical project take?',
            '{{FAQ_1_ANSWER}}' => 'Most projects are completed within 4-8 weeks, depending on complexity and scope.',
            '{{FAQ_2_QUESTION}}' => 'Do you provide ongoing support?',
            '{{FAQ_2_ANSWER}}' => 'Yes, we offer comprehensive support and maintenance packages for all our clients.',
            '{{FAQ_3_QUESTION}}' => 'What is included in your pricing?',
            '{{FAQ_3_ANSWER}}' => 'Our pricing includes design, development, testing, deployment, and initial training.',
            '{{FAQ_4_QUESTION}}' => 'Can you work with our existing systems?',
            '{{FAQ_4_ANSWER}}' => 'Absolutely! We specialize in integrating with existing systems and databases.',
            '{{FAQ_5_QUESTION}}' => 'Do you offer custom development?',
            '{{FAQ_5_ANSWER}}' => 'Yes, we provide fully custom development solutions tailored to your specific needs.',
            '{{FAQ_6_QUESTION}}' => 'What technologies do you use?',
            '{{FAQ_6_ANSWER}}' => 'We use modern, industry-standard technologies including React, WordPress, PHP, and more.',
            '{{FAQ_7_QUESTION}}' => 'Do you provide training?',
            '{{FAQ_7_ANSWER}}' => 'Yes, we provide comprehensive training to ensure your team can manage the solution effectively.',
            '{{FAQ_8_QUESTION}}' => 'What about mobile responsiveness?',
            '{{FAQ_8_ANSWER}}' => 'All our solutions are fully responsive and optimized for mobile devices.',
            '{{FAQ_9_QUESTION}}' => 'Can you help with SEO?',
            '{{FAQ_9_ANSWER}}' => 'Yes, we include SEO optimization in all our web development projects.',
            '{{FAQ_10_QUESTION}}' => 'What is your refund policy?',
            '{{FAQ_10_ANSWER}}' => 'We offer a satisfaction guarantee and will work with you to ensure project success.',

            // Guarantees placeholders
            '{{GUARANTEES_1_TITLE}}' => 'Quality Assurance',
            '{{GUARANTEE_1_ITEM_1_TITLE}}' => '100% Satisfaction Guarantee',
            '{{GUARANTEE_1_ITEM_1_DESC}}' => 'We ensure complete satisfaction with every project',
            '{{GUARANTEE_1_ITEM_2_TITLE}}' => 'On-Time Delivery',
            '{{GUARANTEE_1_ITEM_2_DESC}}' => 'Projects delivered on schedule, every time',
            '{{GUARANTEE_1_ITEM_3_TITLE}}' => 'Quality Code',
            '{{GUARANTEE_1_ITEM_3_DESC}}' => 'Clean, maintainable, and well-documented code',
            '{{GUARANTEE_1_ITEM_4_TITLE}}' => 'Security First',
            '{{GUARANTEE_1_ITEM_4_DESC}}' => 'Built with security best practices from day one',
            '{{GUARANTEE_1_ITEM_5_TITLE}}' => 'Performance Optimized',
            '{{GUARANTEE_1_ITEM_5_DESC}}' => 'Fast loading and optimized for all devices',
            '{{GUARANTEE_1_ITEM_6_TITLE}}' => 'SEO Ready',
            '{{GUARANTEE_1_ITEM_6_DESC}}' => 'Optimized for search engines and visibility',

            '{{GUARANTEES_2_TITLE}}' => 'Support & Service',
            '{{GUARANTEE_2_ITEM_1_TITLE}}' => '24/7 Support',
            '{{GUARANTEE_2_ITEM_1_DESC}}' => 'Round-the-clock support when you need it',
            '{{GUARANTEE_2_ITEM_2_TITLE}}' => 'Free Updates',
            '{{GUARANTEE_2_ITEM_2_DESC}}' => 'Regular updates and improvements included',
            '{{GUARANTEE_2_ITEM_3_TITLE}}' => 'Backup & Recovery',
            '{{GUARANTEE_2_ITEM_3_DESC}}' => 'Automated backups and disaster recovery',
            '{{GUARANTEE_2_ITEM_4_TITLE}}' => 'Training Included',
            '{{GUARANTEE_2_ITEM_4_DESC}}' => 'Comprehensive training for your team',
            '{{GUARANTEE_2_ITEM_5_TITLE}}' => 'Documentation',
            '{{GUARANTEE_2_ITEM_5_DESC}}' => 'Complete documentation and user guides',
            '{{GUARANTEE_2_ITEM_6_TITLE}}' => 'Scalability',
            '{{GUARANTEE_2_ITEM_6_DESC}}' => 'Solutions that grow with your business',

            // Guide section placeholders
            '{{GUIDE_1_TITLE}}' => 'Getting Started Guide',
            '{{GUIDE_1_ITEM_1_TITLE}}' => 'Planning Your Project',
            '{{GUIDE_1_ITEM_1_DESC}}' => 'Define goals, scope, and requirements',
            '{{GUIDE_1_ITEM_2_TITLE}}' => 'Design Process',
            '{{GUIDE_1_ITEM_2_DESC}}' => 'Create wireframes and visual designs',
            '{{GUIDE_1_ITEM_3_TITLE}}' => 'Development Phase',
            '{{GUIDE_1_ITEM_3_DESC}}' => 'Build and test your solution',
            '{{GUIDE_1_ITEM_4_TITLE}}' => 'Launch Preparation',
            '{{GUIDE_1_ITEM_4_DESC}}' => 'Final testing and deployment setup',
            '{{GUIDE_1_ITEM_5_TITLE}}' => 'Go Live',
            '{{GUIDE_1_ITEM_5_DESC}}' => 'Launch and monitor performance',

            '{{GUIDE_2_TITLE}}' => 'Best Practices',
            '{{GUIDE_2_ITEM_1_TITLE}}' => 'Content Strategy',
            '{{GUIDE_2_ITEM_1_DESC}}' => 'Create engaging and valuable content',
            '{{GUIDE_2_ITEM_2_TITLE}}' => 'SEO Optimization',
            '{{GUIDE_2_ITEM_2_DESC}}' => 'Optimize for search engines',
            '{{GUIDE_2_ITEM_3_TITLE}}' => 'User Experience',
            '{{GUIDE_2_ITEM_3_DESC}}' => 'Design for your users first',
            '{{GUIDE_2_ITEM_4_TITLE}}' => 'Performance',
            '{{GUIDE_2_ITEM_4_DESC}}' => 'Optimize for speed and efficiency',
            '{{GUIDE_2_ITEM_5_TITLE}}' => 'Security',
            '{{GUIDE_2_ITEM_5_DESC}}' => 'Implement security best practices',

            '{{GUIDE_CTA_TEXT}}' => 'Ready to get started?',
            '{{GUIDE_CTA_DESCRIPTION}}' => 'Download our complete project guide and checklist.',
            '{{GUIDE_CTA_BUTTON}}' => 'Download Free Guide',
            '{{GUIDE_CTA_LINK}}' => '#contact',

            // Locations placeholders
            '{{LOCATION_1_TITLE}}' => 'North America',
            '{{LOCATION_1_DESCRIPTION}}' => 'Serving clients across the United States and Canada',
            '{{LOCATION_1_AREA_1}}' => 'New York',
            '{{LOCATION_1_AREA_2}}' => 'Los Angeles',
            '{{LOCATION_1_AREA_3}}' => 'Chicago',
            '{{LOCATION_1_AREA_4}}' => 'Toronto',

            '{{LOCATION_2_TITLE}}' => 'Europe',
            '{{LOCATION_2_DESCRIPTION}}' => 'Supporting businesses across European markets',
            '{{LOCATION_2_AREA_1}}' => 'London',
            '{{LOCATION_2_AREA_2}}' => 'Berlin',
            '{{LOCATION_2_AREA_3}}' => 'Paris',
            '{{LOCATION_2_AREA_4}}' => 'Amsterdam',

            '{{LOCATION_3_TITLE}}' => 'Asia Pacific',
            '{{LOCATION_3_DESCRIPTION}}' => 'Growing presence in the Asia Pacific region',
            '{{LOCATION_3_AREA_1}}' => 'Tokyo',
            '{{LOCATION_3_AREA_2}}' => 'Singapore',
            '{{LOCATION_3_AREA_3}}' => 'Sydney',
            '{{LOCATION_3_AREA_4}}' => 'Hong Kong',

            // Testimonials placeholders
            '{{TESTIMONIAL_1_COMPANY}}' => 'TechCorp Inc.',
            '{{TESTIMONIAL_2_COMPANY}}' => 'StartupXYZ',
            '{{TESTIMONIAL_3_COMPANY}}' => 'Enterprise Solutions Ltd.',
            '{{TESTIMONIAL_4_COMPANY}}' => 'Digital Agency Pro',
            '{{TESTIMONIAL_5_COMPANY}}' => 'E-commerce Plus',
            '{{TESTIMONIAL_6_COMPANY}}' => 'Innovation Labs',
            '{{TESTIMONIALS_CTA_TEXT}}' => 'Join hundreds of satisfied clients',
            '{{TESTIMONIALS_RATING}}' => '4.9/5 stars from 500+ reviews',

            // About section placeholders
            '{{ABOUT_1_TITLE}}' => 'Our Expertise',
            '{{ABOUT_1_DESCRIPTION}}' => 'With over 10 years of experience, we deliver exceptional results.',
            '{{ABOUT_1_ACHIEVEMENTS_TITLE}}' => 'Key Achievements',
            '{{ABOUT_1_ACHIEVEMENT_1_TITLE}}' => 'Industry Recognition',
            '{{ABOUT_1_ACHIEVEMENT_1_DESC}}' => 'Award-winning solutions and services',
            '{{ABOUT_1_ACHIEVEMENT_2_TITLE}}' => 'Client Success',
            '{{ABOUT_1_ACHIEVEMENT_2_DESC}}' => '500+ successful projects delivered',
            '{{ABOUT_1_ACHIEVEMENT_3_TITLE}}' => 'Team Excellence',
            '{{ABOUT_1_ACHIEVEMENT_3_DESC}}' => 'Certified professionals and experts',
            '{{ABOUT_1_ACHIEVEMENT_4_TITLE}}' => 'Innovation',
            '{{ABOUT_1_ACHIEVEMENT_4_DESC}}' => 'Cutting-edge technology solutions',
            '{{ABOUT_1_ACHIEVEMENT_5_TITLE}}' => 'Support',
            '{{ABOUT_1_ACHIEVEMENT_5_DESC}}' => '24/7 dedicated customer support',

            '{{ABOUT_2_TITLE}}' => 'Our Credentials',
            '{{ABOUT_2_DESCRIPTION}}' => 'Certified and trusted by leading technology partners.',
            '{{ABOUT_2_CREDENTIALS_TITLE}}' => 'Certifications',
            '{{ABOUT_2_CREDENTIAL_1}}' => 'Google Premier Partner',
            '{{ABOUT_2_CREDENTIAL_2}}' => 'AWS Advanced Consulting Partner',
            '{{ABOUT_2_CREDENTIAL_3}}' => 'Microsoft Gold Partner',
            '{{ABOUT_2_CREDENTIAL_4}}' => 'ISO 27001 Certified',
            '{{ABOUT_2_CREDENTIAL_5}}' => 'SOC 2 Type II Compliant',
            '{{ABOUT_2_CREDENTIAL_6}}' => 'GDPR Compliant',

            '{{ABOUT_STAT_1_NUMBER}}' => '500+',
            '{{ABOUT_STAT_1_LABEL}}' => 'Projects Completed',
            '{{ABOUT_STAT_2_NUMBER}}' => '10+',
            '{{ABOUT_STAT_2_LABEL}}' => 'Years Experience',
            '{{ABOUT_STAT_3_NUMBER}}' => '50+',
            '{{ABOUT_STAT_3_LABEL}}' => 'Team Members',
            '{{ABOUT_STAT_4_NUMBER}}' => '99%',
            '{{ABOUT_STAT_4_LABEL}}' => 'Client Satisfaction',

            // Contact section placeholders
            '{{CONTACT_1_TITLE}}' => 'Get In Touch',
            '{{CONTACT_1_DESCRIPTION}}' => 'Ready to start your project? Contact us today for a free consultation.',
            '{{CONTACT_WHATSAPP}}' => '******-567-8900',
            '{{CONTACT_PHONE_CTA}}' => 'Call Now',
            '{{CONTACT_PHONE_LINK}}' => 'tel:+1234567890',
            '{{CONTACT_WHATSAPP_CTA}}' => 'WhatsApp',
            '{{CONTACT_WHATSAPP_LINK}}' => 'https://wa.me/1234567890',

            '{{CONTACT_2_TITLE}}' => 'Limited Time Offer',
            '{{CONTACT_2_OFFER_TEXT}}' => 'Free consultation includes',
            '{{CONTACT_2_OFFER_1}}' => 'Project assessment and planning',
            '{{CONTACT_2_OFFER_2}}' => 'Technology recommendations',
            '{{CONTACT_2_OFFER_3}}' => 'Timeline and budget estimate',
            '{{CONTACT_2_OFFER_4}}' => 'Risk assessment and mitigation',
            '{{CONTACT_2_OFFER_5}}' => 'Success metrics definition',
            '{{CONTACT_2_URGENCY}}' => 'Book your free consultation today!',
            '{{CONTACT_2_CTA_TEXT}}' => 'Schedule Free Consultation',
            '{{CONTACT_2_CTA_LINK}}' => '#contact',

            '{{FINAL_TRUST_1_TEXT}}' => 'Trusted by 500+ companies',
            '{{FINAL_TRUST_2_TEXT}}' => '99% client satisfaction rate',
            '{{FINAL_TRUST_3_TEXT}}' => 'ISO 27001 certified',
            '{{FINAL_TRUST_4_TEXT}}' => '24/7 support available',
            '{{FINAL_TRUST_5_TEXT}}' => 'Money-back guarantee',
        );

        $replacements = array_merge($replacements, $specific_mappings);

        // Perform replacements
        $content = str_replace(array_keys($replacements), array_values($replacements), $template_content);

        // Log replacement info for debugging
        error_log('Rife PG: Total replacements made: ' . count($replacements));
        error_log('Rife PG: Sample replacements: ' . print_r(array_slice($replacements, 0, 10, true), true));

        return $content;
    }

    /**
     * Generate a new WordPress page (old method - keep for compatibility)
     */
    public function generate_page($template_id, $content_data, $style_data) {
        try {
            // Validate input data
            $validation = $this->validate_input($template_id, $content_data, $style_data);
            if (!$validation['valid']) {
                return array(
                    'success' => false,
                    'message' => $validation['message']
                );
            }
            
            // Get template manager
            $template_manager = rife_pg()->template_manager;
            
            // Validate template data
            $template_validation = $template_manager->validate_template_data($template_id, $content_data);
            if ($template_validation !== true) {
                return array(
                    'success' => false,
                    'message' => 'Template validation failed',
                    'errors' => $template_validation
                );
            }
            
            // Generate HTML content
            $html_content = $template_manager->get_template_content($template_id, $content_data, $style_data);
            
            if (!$html_content) {
                return array(
                    'success' => false,
                    'message' => 'Failed to generate template content'
                );
            }
            
            // Create WordPress page
            $page_data = $this->prepare_page_data($content_data, $html_content);
            $page_id = $this->create_wordpress_page($page_data);
            
            if (!$page_id) {
                return array(
                    'success' => false,
                    'message' => 'Failed to create WordPress page'
                );
            }
            
            // Store metadata
            $this->store_page_metadata($page_id, $template_id, $content_data, $style_data);
            
            // Get page URL
            $page_url = get_permalink($page_id);
            $edit_url = get_edit_post_link($page_id);
            
            return array(
                'success' => true,
                'message' => 'Page generated successfully!',
                'page_id' => $page_id,
                'page_url' => $page_url,
                'edit_url' => $edit_url
            );
            
        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => 'An error occurred while generating the page',
                'error' => $e->getMessage()
            );
        }
    }
    
    /**
     * Validate input data
     */
    private function validate_input($template_id, $content_data, $style_data) {
        // Check template ID
        if (empty($template_id)) {
            return array(
                'valid' => false,
                'message' => 'Template ID is required'
            );
        }
        
        // Check if template exists
        $template_manager = rife_pg()->template_manager;
        $template = $template_manager->get_template($template_id);
        
        if (!$template) {
            return array(
                'valid' => false,
                'message' => 'Invalid template ID'
            );
        }
        
        // Check content data
        if (empty($content_data) || !is_array($content_data)) {
            return array(
                'valid' => false,
                'message' => 'Content data is required'
            );
        }
        
        // Check hero section (required for page title)
        if (empty($content_data['hero']['title'])) {
            return array(
                'valid' => false,
                'message' => 'Hero title is required'
            );
        }
        
        return array('valid' => true);
    }
    
    /**
     * Prepare page data for WordPress
     */
    private function prepare_page_data($content_data, $html_content) {
        // Get page title from hero section
        $page_title = sanitize_text_field($content_data['hero']['title']);
        
        // Generate unique slug
        $page_slug = $this->generate_unique_slug($page_title);
        
        // Prepare page data
        $page_data = array(
            'post_title' => $page_title,
            'post_name' => $page_slug,
            'post_content' => $html_content,
            'post_status' => rife_pg()->get_option('auto_publish', false) ? 'publish' : 'draft',
            'post_type' => 'page',
            'post_author' => get_current_user_id(),
            'meta_input' => array(
                '_rife_pg_generated' => true,
                '_rife_pg_version' => RIFE_PG_VERSION
            )
        );
        
        return $page_data;
    }
    
    /**
     * Generate unique slug for the page
     */
    private function generate_unique_slug($title) {
        $base_slug = sanitize_title($title);
        $slug = $base_slug;
        $counter = 1;
        
        // Check if slug exists
        while ($this->slug_exists($slug)) {
            $slug = $base_slug . '-' . $counter;
            $counter++;
        }
        
        return $slug;
    }
    
    /**
     * Check if slug exists
     */
    private function slug_exists($slug) {
        $page = get_page_by_path($slug);
        return !empty($page);
    }
    
    /**
     * Create WordPress page
     */
    private function create_wordpress_page($page_data) {
        // Insert the page
        $page_id = wp_insert_post($page_data);
        
        if (is_wp_error($page_id)) {
            error_log('Rife PG: Failed to create page - ' . $page_id->get_error_message());
            return false;
        }
        
        return $page_id;
    }
    
    /**
     * Store page metadata in custom table
     */
    private function store_page_metadata($page_id, $template_id, $content_data, $style_data) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'rife_pg_pages';
        
        $result = $wpdb->insert(
            $table_name,
            array(
                'page_id' => $page_id,
                'template_id' => $template_id,
                'style_data' => json_encode($style_data),
                'content_data' => json_encode($content_data),
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql')
            ),
            array('%d', '%s', '%s', '%s', '%s', '%s')
        );
        
        if ($result === false) {
            error_log('Rife PG: Failed to store page metadata - ' . $wpdb->last_error);
        }
        
        return $result;
    }
    
    /**
     * Update existing generated page
     */
    public function update_page($page_id, $template_id, $content_data, $style_data) {
        try {
            // Validate input
            $validation = $this->validate_input($template_id, $content_data, $style_data);
            if (!$validation['valid']) {
                return array(
                    'success' => false,
                    'message' => $validation['message']
                );
            }
            
            // Check if page exists and was generated by our plugin
            if (!$this->is_generated_page($page_id)) {
                return array(
                    'success' => false,
                    'message' => 'Page not found or not generated by this plugin'
                    );
            }
            
            // Generate new content
            $template_manager = rife_pg()->template_manager;
            $html_content = $template_manager->get_template_content($template_id, $content_data, $style_data);
            
            if (!$html_content) {
                return array(
                    'success' => false,
                    'message' => 'Failed to generate template content'
                    );
            }
            
            // Update page
            $page_title = sanitize_text_field($content_data['hero']['title']);
            
            $updated_page = array(
                'ID' => $page_id,
                'post_title' => $page_title,
                'post_content' => $html_content
            );
            
            $result = wp_update_post($updated_page);
            
            if (is_wp_error($result)) {
                return array(
                    'success' => false,
                    'message' => 'Failed to update page'
                    );
            }
            
            // Update metadata
            $this->update_page_metadata($page_id, $template_id, $content_data, $style_data);
            
            return array(
                'success' => true,
                'message' => 'Page updated successfully!',
                'page_id' => $page_id,
                'page_url' => get_permalink($page_id)
            );
            
        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => 'An error occurred while updating the page',
                'error' => $e->getMessage()
            );
        }
    }
    
    /**
     * Check if page was generated by our plugin
     */
    private function is_generated_page($page_id) {
        return get_post_meta($page_id, '_rife_pg_generated', true) === true;
    }
    
    /**
     * Update page metadata
     */
    private function update_page_metadata($page_id, $template_id, $content_data, $style_data) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'rife_pg_pages';
        
        $result = $wpdb->update(
            $table_name,
            array(
                'template_id' => $template_id,
                'style_data' => json_encode($style_data),
                'content_data' => json_encode($content_data),
                'updated_at' => current_time('mysql')
            ),
            array('page_id' => $page_id),
            array('%s', '%s', '%s', '%s'),
            array('%d')
        );
        
        return $result;
    }
    
    /**
     * Extract Yoast SEO data from form data
     */
    private function extract_yoast_seo_data($form_data) {
        try {
            error_log('Rife PG: Extracting Yoast SEO data from form data');
            error_log('Rife PG: Form data keys: ' . print_r(array_keys($form_data), true));

            $yoast_data = array();

            // Validate Yoast SEO plugin is active
            if (!$this->is_yoast_seo_active()) {
                error_log('Rife PG: Yoast SEO plugin is not active, skipping SEO data extraction');
                return array();
            }

            // Extract focus keyphrase with validation
            if (!empty($form_data['yoast_focus_keyphrase'])) {
                $focus_kw = sanitize_text_field($form_data['yoast_focus_keyphrase']);

                // Validate keyphrase length (Yoast recommends 1-4 words)
                if ($this->validate_focus_keyphrase($focus_kw)) {
                    $yoast_data['_yoast_wpseo_focuskw'] = $focus_kw;
                    $yoast_data['_yoast_wpseo_focuskw_text_input'] = $focus_kw;
                    error_log('Rife PG: Focus keyphrase extracted: ' . $focus_kw);
                } else {
                    error_log('Rife PG: Invalid focus keyphrase format: ' . $focus_kw);
                }
            }

            // Extract SEO title with validation
            if (!empty($form_data['yoast_seo_title'])) {
                $seo_title = sanitize_text_field($form_data['yoast_seo_title']);

                // Validate title length (recommended 50-60 characters)
                if ($this->validate_seo_title($seo_title)) {
                    $yoast_data['_yoast_wpseo_title'] = $seo_title;
                    error_log('Rife PG: SEO title extracted: ' . $seo_title);
                } else {
                    error_log('Rife PG: SEO title validation warning: ' . strlen($seo_title) . ' characters');
                    $yoast_data['_yoast_wpseo_title'] = $seo_title; // Still use it but log warning
                }
            }

            // Extract meta description with validation
            if (!empty($form_data['yoast_meta_description'])) {
                $meta_desc = sanitize_textarea_field($form_data['yoast_meta_description']);

                // Validate description length (recommended 150-160 characters)
                if ($this->validate_meta_description($meta_desc)) {
                    $yoast_data['_yoast_wpseo_metadesc'] = $meta_desc;
                    error_log('Rife PG: Meta description extracted: ' . substr($meta_desc, 0, 50) . '...');
                } else {
                    error_log('Rife PG: Meta description validation warning: ' . strlen($meta_desc) . ' characters');
                    $yoast_data['_yoast_wpseo_metadesc'] = $meta_desc; // Still use it but log warning
                }
            }

            // Set additional Yoast SEO defaults for better optimization
            if (!empty($yoast_data)) {
                error_log('Rife PG: Setting additional Yoast SEO defaults');

                // Enable SEO analysis
                $yoast_data['_yoast_wpseo_meta-robots-noindex'] = '0';
                $yoast_data['_yoast_wpseo_meta-robots-nofollow'] = '0';
                $yoast_data['_yoast_wpseo_meta-robots-adv'] = 'none';

                // Set content score to indicate content has been optimized
                $yoast_data['_yoast_wpseo_content_score'] = '30';

                // Set linkdex (SEO score) to a reasonable default
                $yoast_data['_yoast_wpseo_linkdex'] = '30';

                // Set primary category if not set
                if (!isset($yoast_data['_yoast_wpseo_primary_category'])) {
                    $yoast_data['_yoast_wpseo_primary_category'] = '';
                }

                // Add schema markup defaults
                $yoast_data['_yoast_wpseo_schema_page_type'] = 'WebPage';
                $yoast_data['_yoast_wpseo_schema_article_type'] = 'None';

                error_log('Rife PG: Yoast data extracted successfully with ' . count($yoast_data) . ' fields');
            } else {
                error_log('Rife PG: No Yoast SEO data to extract');
            }

            return $yoast_data;

        } catch (Exception $e) {
            error_log('Rife PG: Error extracting Yoast SEO data - ' . $e->getMessage());
            error_log('Rife PG: Exception trace: ' . $e->getTraceAsString());
            return array();
        }
    }
    
    /**
     * Delete generated page
     */
    public function delete_page($page_id) {
        // Check if page was generated by our plugin
        if (!$this->is_generated_page($page_id)) {
            return array(
                'success' => false,
                'message' => 'Page not found or not generated by this plugin'
            );
        }
        
        // Delete from WordPress
        $result = wp_delete_post($page_id, true);
        
        if (!$result) {
            return array(
                'success' => false,
                'message' => 'Failed to delete page'
            );
        }
        
        // Delete metadata
        global $wpdb;
        $table_name = $wpdb->prefix . 'rife_pg_pages';
        $wpdb->delete($table_name, array('page_id' => $page_id), array('%d'));
        
        return array(
            'success' => true,
            'message' => 'Page deleted successfully!'
        );
    }

    /**
     * Check if Yoast SEO plugin is active
     */
    private function is_yoast_seo_active() {
        return class_exists('WPSEO_Options') || defined('WPSEO_VERSION');
    }

    /**
     * Validate focus keyphrase format
     */
    private function validate_focus_keyphrase($keyphrase) {
        if (empty($keyphrase)) {
            return false;
        }

        // Check length (should be reasonable for SEO)
        if (strlen($keyphrase) > 100) {
            return false;
        }

        // Check word count (Yoast recommends 1-4 words for focus keyphrase)
        $word_count = str_word_count($keyphrase);
        if ($word_count > 8) { // Allow up to 8 words for flexibility
            error_log('Rife PG: Focus keyphrase has ' . $word_count . ' words (recommended: 1-4)');
        }

        return true;
    }

    /**
     * Validate SEO title length
     */
    private function validate_seo_title($title) {
        if (empty($title)) {
            return false;
        }

        $length = strlen($title);

        // Yoast recommends 50-60 characters for optimal display
        if ($length > 70) {
            error_log('Rife PG: SEO title is too long (' . $length . ' chars, recommended: 50-60)');
        } elseif ($length < 30) {
            error_log('Rife PG: SEO title might be too short (' . $length . ' chars, recommended: 50-60)');
        }

        return true; // Always return true but log warnings
    }

    /**
     * Validate meta description length
     */
    private function validate_meta_description($description) {
        if (empty($description)) {
            return false;
        }

        $length = strlen($description);

        // Yoast recommends 150-160 characters for optimal display
        if ($length > 170) {
            error_log('Rife PG: Meta description is too long (' . $length . ' chars, recommended: 150-160)');
        } elseif ($length < 120) {
            error_log('Rife PG: Meta description might be too short (' . $length . ' chars, recommended: 150-160)');
        }

        return true; // Always return true but log warnings
    }

    /**
     * Generate and set featured image for page
     */
    private function generate_and_set_featured_image($page_id, $keyword) {
        try {
            error_log('Rife PG: Starting image generation for page ' . $page_id . ' with keyword: ' . $keyword);
            
            // Check if GD library is available
            if (!extension_loaded('gd')) {
                error_log('Rife PG: GD library not available, using fallback image');
                return $this->set_fallback_featured_image($page_id, $keyword);
            }
            
            // Initialize image generator
            $image_generator = new Rife_PG_Image_Generator();
            
            // Generate hero section image
            $hero_image = $image_generator->generate_image(
                $keyword . ' Excellence',
                $keyword,
                'hero-' . sanitize_title($keyword) . '-' . time() . '.jpg'
            );
            
            if ($hero_image['success']) {
                error_log('Rife PG: Hero image generated successfully: ' . $hero_image['url']);
                
                // Set as featured image
                $result = $image_generator->set_featured_image(
                    $page_id,
                    $hero_image['path'],
                    $hero_image['url'],
                    $hero_image['alt_text']
                );
                
                if ($result) {
                    error_log('Rife PG: Featured image set successfully for page ' . $page_id);
                    return true;
                } else {
                    error_log('Rife PG: Failed to set featured image for page ' . $page_id);
                    return $this->set_fallback_featured_image($page_id, $keyword);
                }
            } else {
                error_log('Rife PG: Failed to generate hero image: ' . ($hero_image['error'] ?? 'Unknown error'));
                return $this->set_fallback_featured_image($page_id, $keyword);
            }
            
        } catch (Exception $e) {
            error_log('Rife PG: Image generation error - ' . $e->getMessage());
            return $this->set_fallback_featured_image($page_id, $keyword);
        }
    }
    
    /**
     * Set fallback featured image using placeholder
     */
    private function set_fallback_featured_image($page_id, $keyword) {
        try {
            error_log('Rife PG: Using fallback featured image for page ' . $page_id);
            
            // Use a placeholder image service or default WordPress image
            $placeholder_url = 'https://via.placeholder.com/1200x630/4F46E5/FFFFFF?text=' . urlencode($keyword);
            
            // Download and save the placeholder image
            require_once(ABSPATH . 'wp-admin/includes/media.php');
            require_once(ABSPATH . 'wp-admin/includes/file.php');
            require_once(ABSPATH . 'wp-admin/includes/image.php');
            
            $tmp = download_url($placeholder_url);
            if (is_wp_error($tmp)) {
                error_log('Rife PG: Failed to download placeholder image: ' . $tmp->get_error_message());
                return false;
            }
            
            $file_array = array(
                'name' => 'fallback-' . sanitize_title($keyword) . '-' . time() . '.jpg',
                'tmp_name' => $tmp
            );
            
            $attach_id = media_handle_sideload($file_array, $page_id, $keyword . ' services');
            
            if (is_wp_error($attach_id)) {
                @unlink($tmp);
                error_log('Rife PG: Failed to handle placeholder image: ' . $attach_id->get_error_message());
                return false;
            }
            
            // Set ALT text
            update_post_meta($attach_id, '_wp_attachment_image_alt', $keyword . ' services professional team');
            
            $result = set_post_thumbnail($page_id, $attach_id);
            error_log('Rife PG: Fallback featured image set: ' . ($result ? 'success' : 'failed'));
            return $result;
            
        } catch (Exception $e) {
            error_log('Rife PG: Fallback image error - ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Store section images for later use in templates
     */
    private function store_section_images($page_id, $image) {
        $existing_images = get_post_meta($page_id, '_rife_pg_section_images', true);
        if (!is_array($existing_images)) {
            $existing_images = [];
        }
        
        $existing_images[] = [
            'url' => $image['url'],
            'alt' => $image['alt_text'],
            'filename' => $image['filename']
        ];
        
        update_post_meta($page_id, '_rife_pg_section_images', $existing_images);
    }
}