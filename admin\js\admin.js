/**
 * Admin JavaScript for Rife PageGenerator
 */

jQuery(document).ready(function($) {
    'use strict';

    // --- Template Selector Logic ---
    var templateSelector = $('#template-selector-select');
    if (rifePgAdmin.selectedTemplate) {
        templateSelector.val(rifePgAdmin.selectedTemplate);
    }

    // --- Repeater Logic ---
    $(document).on('click', '.add-repeater-item', function() {
        var repeaterField = $(this).closest('.repeater-field');
        var template = repeaterField.siblings('.repeater-item-template').html();
        var itemsContainer = repeaterField.find('.repeater-items');
        var index = itemsContainer.children().length;
        var newItem = template.replace(/__INDEX__/g, index);
        itemsContainer.append(newItem);
    });

    $(document).on('click', '.remove-repeater-item', function() {
        $(this).closest('.repeater-item').remove();
    });

    // --- Form Submission Logic ---
    $('#page-generator-form').on('submit', function(e) {
        e.preventDefault();
        generatePage();
    });

    // --- Bulk Generate Logic ---
    $('body').on('click', '#bulk-generate-button', function() {
        bulkGeneratePages();
    });

    function generatePage() {
        var formData = collectFormData();

        if (!validateFormData(formData)) {
            return;
        }

        $('#generate-page').addClass('loading');

        $.ajax({
            url: rifePgAdmin.ajaxUrl,
            type: 'POST',
            data: {
                action: 'rife_pg_generate_page',
                nonce: rifePgAdmin.nonce,
                template_id: formData.template_id,
                auto_publish: formData.auto_publish,
                form_data: formData
            },
            success: function(response) {
                $('#generate-page').removeClass('loading');
                if (response.success) {
                    alert('Page generated successfully!\nID: ' + response.data.page_id + '\nURL: ' + response.data.page_url);
                } else {
                    alert('Error: ' + response.data);
                }
            },
            error: function() {
                $('#generate-page').removeClass('loading');
                alert('An unknown error occurred.');
            }
        });
    }

    function bulkGeneratePages() {
        var button = $('#bulk-generate-button');
        var statusMessage = $('#bulk-status-message-header');
        var fileInput = $('#bulk-json-file')[0];

        if (!fileInput.files || fileInput.files.length === 0) {
            statusMessage.html('<div class="notice notice-error"><p>Please select a JSON file.</p></div>');
            return;
        }

        var file = fileInput.files[0];
        if (file.type !== 'application/json') {
            statusMessage.html('<div class="notice notice-error"><p>Error: File must be a valid .json file.</p></div>');
            return;
        }

        var collectedData = collectFormData();
        if (!collectedData.template_id) {
            statusMessage.html('<div class="notice notice-error"><p>Error: No template selected. Please load a template first.</p></div>');
            return;
        }

        button.addClass('loading').prop('disabled', true);
        statusMessage.html('<div class="notice notice-info"><p>Processing... This may take a moment.</p></div>');

        var ajaxData = new FormData();
        ajaxData.append('action', 'rife_pg_bulk_generate_ajax');
        ajaxData.append('nonce', rifePgAdmin.nonce);
        ajaxData.append('bulk_file', file);
        ajaxData.append('template_id', collectedData.template_id);
        ajaxData.append('auto_publish', $('#auto_publish_bulk').is(':checked'));
        ajaxData.append('styles', JSON.stringify(collectedData.styles));

        $.ajax({
            url: rifePgAdmin.ajaxUrl,
            type: 'POST',
            data: ajaxData,
            processData: false,
            contentType: false,
            success: function(response) {
                button.removeClass('loading').prop('disabled', false);
                console.log('Bulk generate response:', response); // Debug log
                
                // Check if response has the expected structure
                if (!response) {
                    statusMessage.html('<div class="notice notice-error"><p>No response received from server.</p></div>');
                    return;
                }
                
                if (response.success) {
                    var successMsg = 'Successfully generated ' + response.data.success_count + ' pages.';
                    if (response.data.error_count > 0) {
                        successMsg += ' ' + response.data.error_count + ' rows failed (e.g., missing hero_title).';
                    }
                    statusMessage.html('<div class="notice notice-success"><p>' + successMsg + '</p></div>');
                } else {
                    // Handle error response
                    var errorMsg = 'An error occurred during bulk generation.';
                    if (response.data) {
                        if (typeof response.data === 'string') {
                            errorMsg = 'Error: ' + response.data;
                        } else if (response.data.message) {
                            errorMsg = 'Error: ' + response.data.message;
                        } else if (response.data.errors && Array.isArray(response.data.errors)) {
                            errorMsg = 'Errors:<br>' + response.data.errors.join('<br>');
                        }
                    }
                    statusMessage.html('<div class="notice notice-error" style="white-space: normal;"><p>' + errorMsg + '</p></div>');
                }
            },
            error: function(xhr, status, error) {
                button.removeClass('loading').prop('disabled', false);
                console.error('Bulk generate AJAX error:', xhr, status, error); // Debug log
                
                // Try to parse server response if available
                var errorMsg = 'An unknown error occurred during the AJAX request.';
                if (xhr.responseText) {
                    try {
                        var serverResponse = JSON.parse(xhr.responseText);
                        if (serverResponse && serverResponse.data) {
                            if (typeof serverResponse.data === 'string') {
                                errorMsg = 'Server Error: ' + serverResponse.data;
                            } else if (serverResponse.data.message) {
                                errorMsg = 'Server Error: ' + serverResponse.data.message;
                            }
                        }
                    } catch (parseError) {
                        // If we can't parse the response, show raw response
                        errorMsg = 'Server Error: ' + xhr.responseText.substring(0, 200) + '...';
                    }
                } else if (xhr.status) {
                    errorMsg = 'HTTP Error ' + xhr.status + ': ' + (xhr.statusText || 'Unknown error');
                }
                
                statusMessage.html('<div class="notice notice-error" style="white-space: normal;"><p>' + errorMsg + '</p></div>');
            }
        });
    }

    function collectFormData() {
        var form = $('#page-generator-form');
        var formData = {
            content: {},
            styles: {},
            template_id: form.find('input[name="template_id"]').val(),
            auto_publish: $('#auto_publish_single').is(':checked') // Use the single publish checkbox
        };

        // Collect content data
        form.find('[name^="content["]').each(function() {
            var name = $(this).attr('name');
            var value = $(this).val();

            // Parse content[section][field] format
            var matches = name.match(/content\[([^\]]+)\]\[([^\]]+)\]/);
            if (matches && matches.length === 3) {
                var section = matches[1];
                var field = matches[2];

                if (!formData.content[section]) {
                    formData.content[section] = {};
                }
                formData.content[section][field] = value;
            }
        });

        // Collect style data
        $('.design-controls input, .design-controls select').each(function() {
            var fieldId = $(this).attr('id') || $(this).attr('name');
            if (fieldId) {
                formData.styles[fieldId] = $(this).val();
            }
        });

        return formData;
    }

    function validateFormData(formData) {
        if (!formData.template_id) {
            alert('Error: No template selected.');
            return false;
        }
        if (!formData.content['hero'] || !formData.content['hero']['title']) {
            alert('Error: Hero Title is required.');
            return false;
        }
        return true;
    }

    // --- AI Prompt Generator Logic (Revised with Yoast SEO & Images) ---
    const seoComprehensiveKeys = ["hero_title", "hero_subtitle", "hero_cta_text", "hero_cta_link", "benefit_1_title", "benefit_1_desc", "benefit_2_title", "benefit_2_desc", "benefit_3_title", "benefit_3_desc", "benefit_4_title", "benefit_4_desc", "benefit_5_title", "benefit_5_desc", "benefit_6_title", "benefit_6_desc", "testimonial_1_name", "testimonial_1_text", "testimonial_2_name", "testimonial_2_text", "testimonial_3_name", "testimonial_3_text", "testimonial_4_name", "testimonial_4_text", "testimonial_5_name", "testimonial_5_text", "testimonial_6_name", "testimonial_6_text", "plan_1_title", "plan_1_price", "plan_1_features", "plan_2_title", "plan_2_price", "plan_2_features", "plan_3_title", "plan_3_price", "plan_3_features", "process_title", "process_subtitle", "process_1_title", "process_1_description", "process_2_title", "process_2_description", "process_3_title", "process_3_description", "process_4_title", "process_4_description", "process_5_title", "process_5_description", "process_6_title", "process_6_description", "pricing_title", "pricing_subtitle", "pricing_1_name", "pricing_1_price", "pricing_1_period", "pricing_1_feature_1", "pricing_1_feature_2", "pricing_1_feature_3", "pricing_1_feature_4", "pricing_1_feature_5", "pricing_1_feature_6", "pricing_1_cta_text", "pricing_1_cta_link", "pricing_2_name", "pricing_2_badge", "pricing_2_price", "pricing_2_period", "pricing_2_feature_1", "pricing_2_feature_2", "pricing_2_feature_3", "pricing_2_feature_4", "pricing_2_feature_5", "pricing_2_feature_6", "pricing_2_feature_7", "pricing_2_feature_8", "pricing_2_cta_text", "pricing_2_cta_link", "pricing_3_name", "pricing_3_price", "pricing_3_period", "pricing_3_feature_1", "pricing_3_feature_2", "pricing_3_feature_3", "pricing_3_feature_4", "pricing_3_feature_5", "pricing_3_feature_6", "pricing_3_feature_7", "pricing_3_feature_8", "pricing_3_feature_9", "pricing_3_cta_text", "pricing_3_cta_link", "pricing_consultation_text", "pricing_consultation_cta", "pricing_consultation_link", "faq_1_question", "faq_1_answer", "faq_2_question", "faq_2_answer", "faq_3_question", "faq_3_answer", "faq_4_question", "faq_4_answer", "faq_5_question", "faq_5_answer", "faq_6_question", "faq_6_answer", "faq_7_question", "faq_7_answer", "faq_8_question", "faq_8_answer", "faq_9_question", "faq_9_answer", "faq_10_question", "faq_10_answer", "study_1_title", "study_1_desc", "study_2_title", "study_2_desc", "study_3_title", "study_3_desc", "guarantees_title", "guarantees_subtitle", "guarantees_1_icon", "guarantees_1_title", "guarantee_1_item_1_title", "guarantee_1_item_1_desc", "guarantee_1_item_2_title", "guarantee_1_item_2_desc", "guarantee_1_item_3_title", "guarantee_1_item_3_desc", "guarantee_1_item_4_title", "guarantee_1_item_4_desc", "guarantee_1_item_5_title", "guarantee_1_item_5_desc", "guarantee_1_item_6_title", "guarantee_1_item_6_desc", "guarantees_2_icon", "guarantees_2_title", "guarantee_2_item_1_title", "guarantee_2_item_1_desc", "guarantee_2_item_2_title", "guarantee_2_item_2_desc", "guarantee_2_item_3_title", "guarantee_2_item_3_desc", "guarantee_2_item_4_title", "guarantee_2_item_4_desc", "guarantee_2_item_5_title", "guarantee_2_item_5_desc", "guarantee_2_item_6_title", "guarantee_2_item_6_desc", "guide_title", "guide_subtitle", "guide_1_icon", "guide_1_title", "guide_1_item_1_title", "guide_1_item_1_desc", "guide_1_item_2_title", "guide_1_item_2_desc", "guide_1_item_3_title", "guide_1_item_3_desc", "guide_1_item_4_title", "guide_1_item_4_desc", "guide_1_item_5_title", "guide_1_item_5_desc", "guide_2_icon", "guide_2_title", "guide_2_item_1_title", "guide_2_item_1_desc", "guide_2_item_2_title", "guide_2_item_2_desc", "guide_2_item_3_title", "guide_2_item_3_desc", "guide_2_item_4_title", "guide_2_item_4_desc", "guide_2_item_5_title", "guide_2_item_5_desc", "guide_cta_text", "guide_cta_description", "guide_cta_button", "guide_cta_icon", "guide_cta_link", "locations_title", "locations_subtitle", "location_1_icon", "location_1_title", "location_1_description", "location_1_area_1", "location_1_area_2", "location_1_area_3", "location_1_area_4", "location_2_icon", "location_2_title", "location_2_description", "location_2_area_1", "location_2_area_2", "location_2_area_3", "location_2_area_4", "location_3_icon", "location_3_title", "location_3_description", "location_3_area_1", "location_3_area_2", "location_3_area_3", "location_3_area_4", "about_title", "about_subtitle", "about_description", "about_stats_1_number", "about_stats_1_label", "about_stats_2_number", "about_stats_2_label", "about_stats_3_number", "about_stats_3_label", "about_stats_4_number", "about_stats_4_label", "contact_email", "contact_phone", "contact_address", "final_cta_title", "final_cta_subtitle", "final_cta_button_text", "final_cta_button_link", "yoast_focus_keyphrase", "yoast_seo_title", "yoast_meta_description", "hero_image_alt", "benefit_1_image_alt", "benefit_2_image_alt", "benefit_3_image_alt", "benefit_4_image_alt", "benefit_5_image_alt", "benefit_6_image_alt"];

    function generatePrompt(keywords) {
        const keywordList = keywords.map(k => `- ${k}`).join('\n');
        const keysString = seoComprehensiveKeys.map(k => `"${k}"`).join(", ");
        const exampleJson = `  {
` + seoComprehensiveKeys.map(k => {
    if (k === 'yoast_focus_keyphrase') {
        return `    "${k}": "${keywords[0] || 'keyword'}"`;
    } else if (k === 'yoast_seo_title') {
        return `    "${k}": "${keywords[0] || 'Keyword'} Services | Professional Solutions"`;
    } else if (k === 'yoast_meta_description') {
        return `    "${k}": "Professional ${keywords[0] || 'keyword'} services with proven results. Contact us for a free consultation and transform your business today."`;
    } else if (k === 'hero_title') {
        return `    "${k}": "${keywords[0] || 'Keyword'} Services - Transform Your Business Today"`;
    } else if (k === 'hero_subtitle') {
        return `    "${k}": "Professional ${keywords[0] || 'keyword'} solutions with proven results. Get your free consultation and boost your business with our expert team."`;
    } else if (k === 'benefits_title') {
        return `    "${k}": "Why Choose Our ${keywords[0] || 'Keyword'} Services?"`;
    } else if (k === 'benefits_subtitle') {
        return `    "${k}": "Discover how our ${keywords[0] || 'keyword'} expertise can transform your business results"`;
    } else if (k === 'hero_image_alt') {
        return `    "${k}": "${keywords[0] || 'keyword'} services professional team"`;
    } else if (k.includes('benefit_') && k.includes('_image_alt')) {
        const benefitNum = k.match(/benefit_(\d+)_image_alt/);
        return `    "${k}": "${keywords[0] || 'keyword'} benefit ${benefitNum ? benefitNum[1] : '1'} - professional service"`;
    } else {
        return `    "${k}": "... (Content for ${keywords[0] || 'Keyword 1'}) ..."`;
    }
}).join(",\n") + `
  }`;

        return `You are an expert copywriter specializing in high-converting landing pages and advanced SEO optimization.

Your task is to generate a JSON array containing ${keywords.length} unique objects. Each object will provide the complete text content for a landing page based on one of the following keywords:

${keywordList}

For each object, use the corresponding keyword to create compelling, SEO-optimized marketing copy that includes proper Yoast SEO fields and follows Yoast SEO best practices.

CRITICAL: Each JSON object must have the following keys, exactly as specified. Do not add, remove, or change any keys.

Required Keys:
${keysString}

ADVANCED SEO OPTIMIZATION INSTRUCTIONS:

1. KEYPHRASE DISTRIBUTION:
- Naturally distribute the focus keyphrase throughout the entire text
- Include the keyphrase in: hero title, hero subtitle, benefits titles, process steps, FAQ questions, and throughout body content
- Aim for 1-2% keyphrase density (mention keyphrase every 100-200 words)

2. KEYPHRASE IN INTRODUCTION:
- MUST include the focus keyphrase in the first paragraph (hero_subtitle)
- Make the topic clear immediately by mentioning the keyphrase early

3. KEYPHRASE IN SUBHEADINGS (H2 & H3):
- Include the keyphrase or synonyms in section titles like:
  - benefits_title: "Why Choose Our [keyphrase] Services?"
  - process_title: "Our [keyphrase] Process"
  - pricing_title: "[keyphrase] Pricing Plans"
  - faq_title: "[keyphrase] Frequently Asked Questions"

4. IMAGE ALT TEXTS:
- "hero_image_alt": Include the keyphrase naturally, e.g., "[keyphrase] services professional team"
- "benefit_X_image_alt": Include keyphrase + benefit description, e.g., "[keyphrase] benefit 1 - professional service"

5. CONTENT STRUCTURE:
- Use the keyphrase naturally in benefit descriptions
- Include synonyms and related terms throughout the content
- Write for humans first, search engines second

6. YOAST SEO FIELDS:
- "yoast_focus_keyphrase": Use the exact keyword from your list
- "yoast_seo_title": Create a compelling title under 60 characters that includes the keyword and a benefit/call-to-action
- "yoast_meta_description": Write a persuasive description under 160 characters that includes the keyword and encourages clicks

Example JSON Structure:
[
${exampleJson},
  {
    "hero_title": "${keywords[1] || 'Keyword 2'} Services - Professional Solutions",
    "hero_subtitle": "Expert ${keywords[1] || 'keyword 2'} services that deliver real results. Our proven ${keywords[1] || 'keyword 2'} strategies help businesses grow faster. Get your free consultation today!",
    "benefits_title": "Why Our ${keywords[1] || 'Keyword 2'} Services Stand Out",
    "benefits_subtitle": "Discover the advantages of working with ${keywords[1] || 'keyword 2'} professionals who understand your needs",
    "hero_image_alt": "${keywords[1] || 'keyword 2'} services professional team",
    "benefit_1_image_alt": "${keywords[1] || 'keyword 2'} benefit 1 - professional service",
    "benefit_2_image_alt": "${keywords[1] || 'keyword 2'} benefit 2 - quality assurance",
    "yoast_focus_keyphrase": "${keywords[1] || 'keyword2'}",
    "yoast_seo_title": "${keywords[1] || 'Keyword 2'} Services | Professional Solutions & Results",
    "yoast_meta_description": "Professional ${keywords[1] || 'keyword 2'} services with proven results. Expert team, affordable pricing, and guaranteed satisfaction. Contact us today!",
    // ... and so on for all keys ...
  }
  // ... etc. for all ${keywords.length} keywords ...
]`;
    }

    $('.generate-prompt-button').on('click', function() {
        var templateId = $(this).data('template-id');
        if (templateId === 'seo-comprehensive') {
            $('#ai-prompt-modal').show();
            $('#prompt-keywords').val('').trigger('change');
        }
    });

    $('#ai-prompt-modal .modal-close, #ai-prompt-modal .modal-overlay').on('click', function() {
        $('#ai-prompt-modal').hide();
    });

    $('#prompt-keywords').on('keyup change', function() {
        const keywords = $(this).val().split('\n').map(k => k.trim()).filter(k => k);
        if (keywords.length === 0) {
            keywords.push('[YOUR KEYWORD HERE]');
        }
        const prompt = generatePrompt(keywords);
        $('#ai-prompt-output').val(prompt);
    });

    $('.copy-prompt-button').on('click', function() {
        var promptText = document.getElementById('ai-prompt-output');
        promptText.select();
        promptText.setSelectionRange(0, 99999); // For mobile devices
        document.execCommand('copy');

        var feedback = $('#copy-prompt-feedback');
        var button = $(this);
        var originalText = button.html();

        feedback.show();
        button.html('Copied!');

        setTimeout(function() {
            feedback.hide();
            button.html(originalText);
        }, 2000);
    });
    // Masonry layout initialization - Simplified version
    function initMasonryLayout() {
        const container = $('.content-sections-container');
        if (!container.length) return;

        // Add masonry-layout class to enable CSS Grid masonry
        container.addClass('masonry-layout');

        // Monitor for DOM changes to maintain layout
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' || mutation.type === 'subtree') {
                    // Just ensure the class is still there
                    container.addClass('masonry-layout');
                }
            });
        });

        observer.observe(container[0], {
            childList: true,
            subtree: true,
            attributes: false // No need to monitor attributes
        });
    }

    // Initialize masonry when template is loaded
    if ($('.content-sections-container').length) {
        initMasonryLayout();
    }

    // Re-initialize when template changes
    $(document).on('template-loaded', function() {
        setTimeout(initMasonryLayout, 100);
    });
});